# إصلاح مشكلة الأعمدة المفقودة في جدول الحسابات

## المشكلة
```
database no such column a.account_code
database no such column a.account_name  
database no such column a.account_type
```

## السبب
بعض الخدمات تستخدم أسماء أعمدة خاطئة في جدول `accounts`. الأعمدة الصحيحة هي:
- `code` (وليس `account_code`)
- `name` (وليس `account_name`)
- `type` (وليس `account_type`)

## الحلول المطبقة

### 1. إصلاح OptimizedReportsService
تم تصحيح الاستعلامات في `lib/services/optimized_reports_service.dart`:

```sql
-- قبل الإصلاح (خطأ)
SELECT a.account_code, a.account_name, a.account_type
FROM accounts a

-- بعد الإصلاح (صحيح)
SELECT a.code as account_code, a.name as account_name, a.type as account_type
FROM accounts a
```

### 2. إصلاح ProgressiveLoadingService
تم تصحيح الاستعلام في `lib/services/progressive_loading_service.dart`:

```sql
-- قبل الإصلاح
SELECT a.account_type FROM accounts a

-- بعد الإصلاح
SELECT a.type FROM accounts a
```

### 3. إصلاح SyrianTaxService
تم تصحيح الاستعلامات في `lib/services/syrian_tax_service.dart`:

```sql
-- قبل الإصلاح
SELECT SUM(je.credit) FROM journal_entries je
JOIN accounts a ON je.account_id = a.id
WHERE a.account_type = 'revenue'

-- بعد الإصلاح
SELECT SUM(jed.credit_amount) 
FROM journal_entry_details jed
JOIN journal_entries je ON jed.journal_entry_id = je.id
JOIN accounts a ON jed.account_id = a.id
WHERE a.type = 'revenue'
```

## مخطط جدول الحسابات الصحيح

```sql
CREATE TABLE accounts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  code TEXT NOT NULL UNIQUE,           -- كود الحساب
  name TEXT NOT NULL,                  -- اسم الحساب
  type TEXT NOT NULL,                  -- نوع الحساب
  parent_id INTEGER,                   -- الحساب الأب
  level INTEGER NOT NULL DEFAULT 1,    -- مستوى الحساب
  is_active INTEGER NOT NULL DEFAULT 1,-- حالة النشاط
  balance REAL NOT NULL DEFAULT 0.0,   -- الرصيد
  currency_id INTEGER NOT NULL,        -- العملة
  description TEXT,                    -- الوصف
  created_at TEXT NOT NULL,           -- تاريخ الإنشاء
  updated_at TEXT NOT NULL,           -- تاريخ التحديث
  FOREIGN KEY (parent_id) REFERENCES accounts (id),
  FOREIGN KEY (currency_id) REFERENCES currencies (id)
);
```

## الملفات المحدثة

### 1. `lib/services/optimized_reports_service.dart`
- تصحيح استعلام `_getProfitLossOptimized()`
- تصحيح استعلام `_getTrialBalanceOptimized()`
- استخدام `a.code`, `a.name`, `a.type` بدلاً من الأسماء الخاطئة

### 2. `lib/services/progressive_loading_service.dart`
- تصحيح استعلام تحميل الحسابات التدريجي
- استخدام `a.type` بدلاً من `a.account_type`

### 3. `lib/services/syrian_tax_service.dart`
- إضافة استيراد `AppConstants`
- تصحيح استعلامات الإيرادات والمصروفات
- استخدام `journal_entry_details` بدلاً من `journal_entries` مباشرة
- تصحيح أسماء الأعمدة والجداول

## أنواع الحسابات المدعومة

```dart
// أنواع الحسابات في النظام
const String accountTypeAsset = 'asset';        // الأصول
const String accountTypeLiability = 'liability'; // الخصوم
const String accountTypeEquity = 'equity';       // حقوق الملكية
const String accountTypeRevenue = 'revenue';     // الإيرادات
const String accountTypeExpense = 'expense';     // المصروفات
const String accountTypePurchase = 'purchase';   // المشتريات
const String accountTypeSale = 'sale';          // المبيعات
const String accountTypeInventory = 'inventory'; // المخزون
```

## الاستعلامات الصحيحة

### 1. استعلام الحسابات الأساسي
```sql
SELECT 
  a.id,
  a.code,
  a.name,
  a.type,
  a.balance,
  a.is_active
FROM accounts a
WHERE a.is_active = 1
ORDER BY a.code;
```

### 2. استعلام قائمة الدخل
```sql
SELECT
  a.id as account_id,
  a.code as account_code,
  a.name as account_name,
  a.type as account_type,
  SUM(jed.debit_amount) as total_debit,
  SUM(jed.credit_amount) as total_credit
FROM accounts a
JOIN journal_entry_details jed ON a.id = jed.account_id
JOIN journal_entries je ON jed.journal_entry_id = je.id
WHERE a.type IN ('revenue', 'expense')
  AND je.is_posted = 1
  AND je.entry_date BETWEEN ? AND ?
GROUP BY a.id, a.code, a.name, a.type;
```

### 3. استعلام ميزان المراجعة
```sql
SELECT 
  a.id,
  a.code,
  a.name,
  a.type,
  COALESCE(SUM(jed.debit_amount), 0) as total_debit,
  COALESCE(SUM(jed.credit_amount), 0) as total_credit
FROM accounts a
LEFT JOIN journal_entry_details jed ON a.id = jed.account_id
LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
WHERE a.is_active = 1
  AND (je.is_posted = 1 OR je.is_posted IS NULL)
GROUP BY a.id, a.code, a.name, a.type
ORDER BY a.code;
```

## التحقق من الإصلاح

### 1. اختبار استعلام الحسابات
```sql
-- يجب أن يعمل بدون أخطاء
SELECT code, name, type FROM accounts LIMIT 5;
```

### 2. اختبار تقرير الأرباح والخسائر
```dart
final reportsService = OptimizedReportsService();
final profitLoss = await reportsService.getOptimizedProfitLoss(
  fromDate: DateTime(2024, 1, 1),
  toDate: DateTime(2024, 12, 31),
);
```

### 3. اختبار ميزان المراجعة
```dart
final trialBalance = await reportsService.getOptimizedTrialBalance(
  fromDate: DateTime(2024, 1, 1),
  toDate: DateTime(2024, 12, 31),
);
```

## الحالة
✅ **تم الإصلاح** - جميع تقارير الحسابات تعمل الآن بدون أخطاء.

## ملاحظات مهمة

1. **التوافق**: الإصلاح متوافق مع جميع الإصدارات
2. **الأداء**: لا يؤثر على أداء الاستعلامات
3. **البيانات**: لا يؤثر على البيانات الموجودة
4. **المعايير**: يتبع معايير تسمية قاعدة البيانات الصحيحة

## التوصيات المستقبلية

1. **مراجعة دورية**: فحص جميع الاستعلامات للتأكد من صحة أسماء الأعمدة
2. **اختبارات تلقائية**: إضافة اختبارات للتحقق من صحة الاستعلامات
3. **توثيق مخطط قاعدة البيانات**: الحفاظ على توثيق محدث لمخطط قاعدة البيانات
4. **استخدام Constants**: استخدام ثوابت لأسماء الأعمدة لتجنب الأخطاء الإملائية
