# إصلاح مشكلة جداول الموارد البشرية

## المشكلة
```
database no such table attendance
```

## السبب
جداول الموارد البشرية (attendance, leaves, loans, departments, positions) مفقودة من قاعدة البيانات.

## الحلول المطبقة

### 1. إضافة فحص جداول HR في DatabaseMigrationService
تم إضافة دالة `_ensureHRTablesExist()` التي تتحقق من وجود جميع جداول الموارد البشرية وتنشئها إذا كانت مفقودة:

```dart
// الجداول المضافة:
- attendance (الحضور والانصراف)
- leaves (الإجازات)
- loans (القروض)
- departments (الأقسام)
- positions (المناصب)
```

### 2. إضافة معالجة آمنة في HRReportsService
تم تحديث خدمة تقارير الموارد البشرية للتعامل مع الجداول المفقودة:

```dart
// فحص وجود الجدول قبل الاستعلام
final tableExists = await _checkTableExists(db, AppConstants.attendanceTable);
if (!tableExists) {
  // إرجاع تقرير فارغ
  return AttendanceReport(...);
}
```

### 3. تحديث عملية التهيئة
تم إضافة فحص جداول HR في عملية التهيئة الأساسية:

```dart
await _ensureHRTablesExist(db);
```

## الجداول المنشأة

### 1. جدول الحضور (attendance)
```sql
CREATE TABLE attendance (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  employee_id INTEGER NOT NULL,
  attendance_date TEXT NOT NULL,
  check_in_time TEXT,
  check_out_time TEXT,
  break_start_time TEXT,
  break_end_time TEXT,
  total_hours REAL NOT NULL DEFAULT 0,
  regular_hours REAL NOT NULL DEFAULT 0,
  overtime_hours REAL NOT NULL DEFAULT 0,
  late_minutes INTEGER NOT NULL DEFAULT 0,
  early_leave_minutes INTEGER NOT NULL DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'present',
  notes TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
  UNIQUE(employee_id, attendance_date)
);
```

### 2. جدول الإجازات (leaves)
```sql
CREATE TABLE leaves (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  employee_id INTEGER NOT NULL,
  leave_type TEXT NOT NULL DEFAULT 'annual',
  start_date TEXT NOT NULL,
  end_date TEXT NOT NULL,
  total_days INTEGER NOT NULL,
  reason TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  requested_by INTEGER,
  approved_by INTEGER,
  approved_at TEXT,
  rejection_reason TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
);
```

### 3. جدول القروض (loans)
```sql
CREATE TABLE loans (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  employee_id INTEGER NOT NULL,
  loan_number TEXT NOT NULL UNIQUE,
  amount REAL NOT NULL,
  interest_rate REAL NOT NULL DEFAULT 0,
  installments_count INTEGER NOT NULL,
  monthly_installment REAL NOT NULL,
  remaining_amount REAL NOT NULL,
  start_date TEXT NOT NULL,
  end_date TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  purpose TEXT,
  guarantor_id INTEGER,
  notes TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
  FOREIGN KEY (guarantor_id) REFERENCES employees (id) ON DELETE SET NULL
);
```

### 4. جدول الأقسام (departments)
```sql
CREATE TABLE departments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  code TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  description TEXT,
  manager_id INTEGER,
  cost_center_account_id INTEGER,
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (manager_id) REFERENCES employees (id) ON DELETE SET NULL,
  FOREIGN KEY (cost_center_account_id) REFERENCES accounts (id) ON DELETE SET NULL
);
```

### 5. جدول المناصب (positions)
```sql
CREATE TABLE positions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  code TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  description TEXT,
  department_id INTEGER,
  level INTEGER NOT NULL DEFAULT 1,
  min_salary REAL DEFAULT 0,
  max_salary REAL DEFAULT 0,
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (department_id) REFERENCES departments (id) ON DELETE SET NULL
);
```

## الملفات المحدثة

1. `lib/services/database_migration_service.dart`
   - إضافة `_ensureHRTablesExist()`
   - تحديث `ensureAllRequiredColumnsExist()`

2. `lib/services/hr_reports_service.dart`
   - إضافة `_checkTableExists()`
   - تحديث `generateAttendanceReport()`
   - إضافة معالجة آمنة للجداول المفقودة

## كيفية تطبيق الإصلاح

### تلقائياً
الإصلاح يتم تلقائياً عند تشغيل التطبيق من خلال:
```dart
await migrationService.fixCommonDatabaseIssues();
```

### يدوياً (إذا لزم الأمر)
يمكن تشغيل الإصلاح يدوياً:
```dart
final migrationService = DatabaseMigrationService();
await migrationService.ensureAllRequiredColumnsExist();
```

## التحقق من الإصلاح

```dart
// فحص وجود الجداول
final migrationService = DatabaseMigrationService();
final attendanceExists = await migrationService.tableExists('attendance');
final leavesExists = await migrationService.tableExists('leaves');
final loansExists = await migrationService.tableExists('loans');

print('جدول الحضور موجود: $attendanceExists');
print('جدول الإجازات موجود: $leavesExists');
print('جدول القروض موجود: $loansExists');
```

## الحالة
✅ **تم الإصلاح** - لوحة تحكم الموارد البشرية تعمل الآن بدون أخطاء.

## ملاحظات
- الإصلاح متوافق مع الإصدارات السابقة
- لا يؤثر على البيانات الموجودة
- يضمن عمل جميع ميزات الموارد البشرية
- يتضمن معالجة آمنة للأخطاء
