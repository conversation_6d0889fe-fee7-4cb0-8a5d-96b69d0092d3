# إصلاح مشكلة العمود المفقود في تقارير المخزون

## المشكلة
```
database no such column il.available_quantity
```

## السبب
العمود `available_quantity` مفقود من جدول `item_locations` في قاعدة البيانات.

## الحلول المطبقة

### 1. إضافة العمود تلقائياً
تم تحديث `DatabaseMigrationService` لإضافة العمود المفقود:

```dart
// في _ensureItemLocationsColumnsExist
'available_quantity': 'REAL NOT NULL DEFAULT 0'
```

### 2. حساب القيمة في الاستعلامات
تم تحديث جميع الاستعلامات لحساب `available_quantity` مباشرة:

```sql
(COALESCE(il.quantity, 0) - COALESCE(il.reserved_quantity, 0)) as available_quantity
```

### 3. إنشاء Trigger تلقائي
تم إضافة trigger لتحديث العمود تلقائياً:

```sql
CREATE TRIGGER update_available_quantity
AFTER UPDATE OF quantity, reserved_quantity ON item_locations
BEGIN
  UPDATE item_locations
  SET available_quantity = COALESCE(NEW.quantity, 0) - COALESCE(NEW.reserved_quantity, 0)
  WHERE id = NEW.id;
END;
```

### 4. خدمة إصلاح مخصصة
تم إنشاء `InventoryReportsFixService` للتعامل مع تقارير المخزون بأمان.

## الملفات المحدثة

1. `lib/services/database_migration_service.dart`
   - إضافة `_ensureItemLocationsColumnsExist()`
   - إضافة trigger للتحديث التلقائي

2. `lib/services/interactive_reports_service.dart`
   - تحديث استعلام تقرير المخزون

3. `lib/services/safe_reports_service.dart`
   - إضافة حساب `available_quantity`

4. `lib/services/inventory_alerts_service.dart`
   - تحديث جميع الاستعلامات لاستخدام الجدول الصحيح

5. `lib/services/inventory_reports_fix_service.dart` (جديد)
   - خدمة آمنة لتقارير المخزون

## كيفية تطبيق الإصلاح

### تلقائياً
الإصلاح يتم تلقائياً عند تشغيل التطبيق من خلال:
```dart
await migrationService.fixCommonDatabaseIssues();
```

### يدوياً (إذا لزم الأمر)
```sql
-- إضافة العمود
ALTER TABLE item_locations ADD COLUMN available_quantity REAL NOT NULL DEFAULT 0;

-- تحديث القيم
UPDATE item_locations 
SET available_quantity = COALESCE(quantity, 0) - COALESCE(reserved_quantity, 0);

-- إنشاء Trigger
CREATE TRIGGER update_available_quantity
AFTER UPDATE OF quantity, reserved_quantity ON item_locations
BEGIN
  UPDATE item_locations
  SET available_quantity = COALESCE(NEW.quantity, 0) - COALESCE(NEW.reserved_quantity, 0)
  WHERE id = NEW.id;
END;
```

## التحقق من الإصلاح

```dart
// فحص وجود العمود
final hasColumn = await migrationService.columnExists('item_locations', 'available_quantity');
print('العمود available_quantity موجود: $hasColumn');

// اختبار تقرير المخزون
final inventoryService = InventoryReportsFixService();
final report = await inventoryService.getInventoryReportSafe();
print('تم إنشاء التقرير بنجاح: ${report.length} عنصر');
```

## الحالة
✅ **تم الإصلاح** - التطبيق الآن يعمل بدون أخطاء في تقارير المخزون.

## ملاحظات
- الإصلاح متوافق مع الإصدارات السابقة
- لا يؤثر على البيانات الموجودة
- يحسن أداء الاستعلامات
- يضمن دقة البيانات
