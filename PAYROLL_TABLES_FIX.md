# إصلاح مشكلة جداول الرواتب

## المشكلة
```
database no such table payroll
```

## السبب
جداول الرواتب (payroll, payroll_details, salary_details, payroll_records) مفقودة من قاعدة البيانات.

## الحلول المطبقة

### 1. إضافة فحص جداول الرواتب في DatabaseMigrationService
تم إضافة دالة `_ensurePayrollTablesExist()` التي تتحقق من وجود جميع جداول الرواتب وتنشئها إذا كانت مفقودة:

```dart
// الجداول المضافة:
- payroll (كشوف الرواتب الرئيسية)
- payroll_details (تفاصيل كشوف الرواتب)
- salary_details (تفاصيل عناصر الراتب)
- payroll_records (سجلات الرواتب الفردية)
```

### 2. إضافة معالجة آمنة في PayrollService
تم تحديث خدمة الرواتب للتعامل مع الجداول المفقودة:

```dart
// فحص وجود الجدول قبل الاستعلام
final tableExists = await _checkTableExists(db, 'payroll_records');
if (!tableExists) {
  LoggingService.warning('جدول الرواتب غير موجود');
  return null;
}
```

### 3. تحديث عملية التهيئة
تم إضافة فحص جداول الرواتب في عملية التهيئة الأساسية:

```dart
await _ensurePayrollTablesExist(db);
```

## الجداول المنشأة

### 1. جدول كشوف الرواتب (payroll)
```sql
CREATE TABLE payroll (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  payroll_number TEXT NOT NULL UNIQUE,
  month INTEGER NOT NULL,
  year INTEGER NOT NULL,
  pay_date TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft',
  total_basic_salary REAL NOT NULL DEFAULT 0,
  total_allowances REAL NOT NULL DEFAULT 0,
  total_deductions REAL NOT NULL DEFAULT 0,
  total_tax REAL NOT NULL DEFAULT 0,
  total_insurance REAL NOT NULL DEFAULT 0,
  total_net_salary REAL NOT NULL DEFAULT 0,
  notes TEXT,
  created_by INTEGER,
  approved_by INTEGER,
  approved_at TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL,
  FOREIGN KEY (approved_by) REFERENCES users (id) ON DELETE SET NULL
);
```

### 2. جدول تفاصيل كشوف الرواتب (payroll_details)
```sql
CREATE TABLE payroll_details (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  payroll_id INTEGER NOT NULL,
  employee_id INTEGER NOT NULL,
  basic_salary REAL NOT NULL DEFAULT 0,
  allowances REAL NOT NULL DEFAULT 0,
  overtime_amount REAL NOT NULL DEFAULT 0,
  bonus_amount REAL NOT NULL DEFAULT 0,
  deductions REAL NOT NULL DEFAULT 0,
  loan_deduction REAL NOT NULL DEFAULT 0,
  tax_amount REAL NOT NULL DEFAULT 0,
  insurance_amount REAL NOT NULL DEFAULT 0,
  net_salary REAL NOT NULL DEFAULT 0,
  working_days INTEGER NOT NULL DEFAULT 0,
  actual_working_days INTEGER NOT NULL DEFAULT 0,
  overtime_hours REAL NOT NULL DEFAULT 0,
  late_hours REAL NOT NULL DEFAULT 0,
  absence_days INTEGER NOT NULL DEFAULT 0,
  notes TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (payroll_id) REFERENCES payroll (id) ON DELETE CASCADE,
  FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
);
```

### 3. جدول تفاصيل عناصر الراتب (salary_details)
```sql
CREATE TABLE salary_details (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  employee_id INTEGER NOT NULL,
  component_type TEXT NOT NULL,
  component_name TEXT NOT NULL,
  amount REAL NOT NULL,
  is_percentage INTEGER NOT NULL DEFAULT 0,
  percentage_of TEXT,
  is_taxable INTEGER NOT NULL DEFAULT 1,
  is_active INTEGER NOT NULL DEFAULT 1,
  effective_from TEXT NOT NULL,
  effective_to TEXT,
  notes TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
);
```

### 4. جدول سجلات الرواتب الفردية (payroll_records)
```sql
CREATE TABLE payroll_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  employee_id INTEGER NOT NULL,
  month INTEGER NOT NULL,
  year INTEGER NOT NULL,
  basic_salary REAL NOT NULL DEFAULT 0,
  allowances REAL NOT NULL DEFAULT 0,
  bonuses REAL NOT NULL DEFAULT 0,
  overtime_amount REAL NOT NULL DEFAULT 0,
  gross_salary REAL NOT NULL DEFAULT 0,
  income_tax REAL NOT NULL DEFAULT 0,
  social_insurance REAL NOT NULL DEFAULT 0,
  loan_deductions REAL NOT NULL DEFAULT 0,
  other_deductions REAL NOT NULL DEFAULT 0,
  total_deductions REAL NOT NULL DEFAULT 0,
  net_salary REAL NOT NULL DEFAULT 0,
  working_days INTEGER NOT NULL DEFAULT 0,
  actual_working_days INTEGER NOT NULL DEFAULT 0,
  overtime_hours REAL NOT NULL DEFAULT 0,
  absence_days INTEGER NOT NULL DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'calculated',
  paid_date TEXT,
  notes TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
  UNIQUE(employee_id, month, year)
);
```

## الملفات المحدثة

1. `lib/services/database_migration_service.dart`
   - إضافة `_ensurePayrollTablesExist()`
   - تحديث `ensureAllRequiredColumnsExist()`

2. `lib/services/payroll_service.dart`
   - إضافة `_checkTableExists()`
   - تحديث `getPayrollRecord()`
   - إضافة معالجة آمنة للجداول المفقودة
   - تحديث استخدام الجداول الصحيحة

## كيفية تطبيق الإصلاح

### تلقائياً
الإصلاح يتم تلقائياً عند تشغيل التطبيق من خلال:
```dart
await migrationService.fixCommonDatabaseIssues();
```

### يدوياً (إذا لزم الأمر)
يمكن تشغيل الإصلاح يدوياً:
```dart
final migrationService = DatabaseMigrationService();
await migrationService.ensureAllRequiredColumnsExist();
```

## التحقق من الإصلاح

```dart
// فحص وجود الجداول
final migrationService = DatabaseMigrationService();
final payrollExists = await migrationService.tableExists('payroll');
final payrollDetailsExists = await migrationService.tableExists('payroll_details');
final salaryDetailsExists = await migrationService.tableExists('salary_details');
final payrollRecordsExists = await migrationService.tableExists('payroll_records');

print('جدول كشوف الرواتب موجود: $payrollExists');
print('جدول تفاصيل الرواتب موجود: $payrollDetailsExists');
print('جدول عناصر الراتب موجود: $salaryDetailsExists');
print('جدول سجلات الرواتب موجود: $payrollRecordsExists');
```

## الميزات المدعومة

### 1. حساب الرواتب المتقدم
- حساب الراتب الأساسي المتناسب
- حساب الساعات الإضافية
- حساب البدلات والحوافز
- حساب الضرائب والتأمينات السورية
- حساب خصم القروض

### 2. إدارة مكونات الراتب
- مكونات راتب مخصصة لكل موظف
- مكونات نسبة مئوية أو مبلغ ثابت
- تواريخ سريان فعالة
- تصنيف المكونات (بدلات، حوافز، استقطاعات)

### 3. التكامل المحاسبي
- قيود محاسبية تلقائية للرواتب
- ربط مع حسابات التكاليف
- تتبع المدفوعات والاستحقاقات

### 4. التقارير والإحصائيات
- تقارير رواتب شهرية
- إحصائيات الأقسام
- تحليل التكاليف
- تقارير الضرائب والتأمينات

## الحالة
✅ **تم الإصلاح** - نظام إدارة الرواتب يعمل الآن بدون أخطاء.

## ملاحظات
- الإصلاح متوافق مع الإصدارات السابقة
- لا يؤثر على البيانات الموجودة
- يدعم النظام الضريبي السوري
- يتضمن معالجة آمنة للأخطاء
- يدعم التكامل المحاسبي الكامل
