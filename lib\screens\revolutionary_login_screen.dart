import 'package:flutter/material.dart';
import '../services/encryption_service.dart';
import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/app_logo.dart';
import 'revolutionary_home_screen.dart';
import 'password_setup_screen.dart';

/// شاشة تسجيل الدخول الثورية مع تصميم سوري أصيل
class RevolutionaryLoginScreen extends StatefulWidget {
  const RevolutionaryLoginScreen({super.key});

  @override
  State<RevolutionaryLoginScreen> createState() =>
      _RevolutionaryLoginScreenState();
}

class _RevolutionaryLoginScreenState extends State<RevolutionaryLoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;
  int _failedAttempts = 0;
  static const int _maxFailedAttempts = 5;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _shakeController;
  late AnimationController _glowController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shakeAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _checkEncryptionSetup();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutBack),
        );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _shakeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticInOut),
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
    Future.delayed(const Duration(milliseconds: 400), () {
      _scaleController.forward();
    });
    _glowController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _shakeController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  Future<void> _checkEncryptionSetup() async {
    try {
      final isSetup = await EncryptionService.isEncryptionSetup();
      if (!isSetup && mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const PasswordSetupScreen(isFirstTime: true),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في فحص إعداد التشفير: $e');
      // في حالة الخطأ، سنفترض أن التشفير غير مُعد
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const PasswordSetupScreen(isFirstTime: true),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RevolutionaryColors.jasmineGradient,
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: Listenable.merge([
              _fadeController,
              _slideController,
              _scaleController,
              _shakeController,
              _glowController,
            ]),
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 40),

                      // الشعار والعنوان الثوري
                      SlideTransition(
                        position: _slideAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildRevolutionaryHeader(),
                        ),
                      ),

                      const SizedBox(height: 50),

                      // النموذج الثوري
                      SlideTransition(
                        position: _slideAnimation,
                        child: _buildRevolutionaryForm(),
                      ),

                      const SizedBox(height: 24),

                      // زر تسجيل الدخول الثوري
                      ScaleTransition(
                        scale: _scaleAnimation,
                        child: _buildRevolutionaryLoginButton(),
                      ),

                      if (_errorMessage != null) ...[
                        const SizedBox(height: 16),
                        _buildRevolutionaryErrorMessage(),
                      ],

                      const SizedBox(height: 32),

                      // خيارات إضافية ثورية
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildRevolutionaryAdditionalOptions(),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildRevolutionaryHeader() {
    return Column(
      children: [
        // الشعار مع تأثير الوهج الثوري
        Container(
          width: 160,
          height: 160,
          decoration: BoxDecoration(
            gradient: RevolutionaryColors.damascusGradient,
            shape: BoxShape.circle,
            boxShadow: [
              RevolutionaryColors.createGlowShadow(
                color: RevolutionaryColors.syrianGold,
                blurRadius: 25 * _glowAnimation.value,
                spreadRadius: 8 * _glowAnimation.value,
              ),
              RevolutionaryColors.createGlowShadow(
                color: RevolutionaryColors.damascusSky,
                blurRadius: 15,
                spreadRadius: 3,
              ),
            ],
          ),
          child: Center(child: AppLogo(size: 90)),
        ),

        const SizedBox(height: 30),

        // العنوان بتدرج ثوري
        ShaderMask(
          shaderCallback: (bounds) =>
              RevolutionaryColors.goldGradient.createShader(bounds),
          child: Text(
            AppConstants.appNameArabic,
            style: const TextStyle(
              fontSize: 42,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 2,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ),

        const SizedBox(height: 12),

        // العنوان الفرعي
        Text(
          'نظام محاسبة متكامل للشركات السورية',
          style: TextStyle(
            fontSize: 18,
            color: RevolutionaryColors.textSecondary,
            fontWeight: FontWeight.w500,
            fontFamily: 'Cairo',
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 25),

        // رسالة ترحيبية
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                RevolutionaryColors.syrianGold.withValues(alpha: 0.1),
                RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: RevolutionaryColors.syrianGold.withValues(alpha: 0.3),
              width: 1.5,
            ),
          ),
          child: Text(
            'أدخل كلمة مرور قاعدة البيانات للمتابعة',
            style: TextStyle(
              fontSize: 16,
              color: RevolutionaryColors.damascusSky,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildRevolutionaryForm() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            RevolutionaryColors.jasmineWhite.withValues(alpha: 0.95),
            RevolutionaryColors.jasminePetal.withValues(alpha: 0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          RevolutionaryColors.createGlowShadow(
            color: RevolutionaryColors.damascusSky,
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
        border: Border.all(
          color: RevolutionaryColors.syrianGold.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      padding: const EdgeInsets.all(28),
      child: Form(
        key: _formKey,
        child: TextFormField(
          controller: _passwordController,
          obscureText: !_isPasswordVisible,
          enabled: !_isLoading,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            fontFamily: 'Cairo',
          ),
          decoration: InputDecoration(
            labelText: 'كلمة مرور قاعدة البيانات',
            labelStyle: TextStyle(
              color: RevolutionaryColors.damascusSky,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
            prefixIcon: Icon(
              Icons.lock_outline,
              color: RevolutionaryColors.syrianGold,
              size: 24,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                color: RevolutionaryColors.damascusSky,
              ),
              onPressed: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(
                color: RevolutionaryColors.syrianGold.withValues(alpha: 0.4),
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(18),
              borderSide: BorderSide(
                color: RevolutionaryColors.damascusSky,
                width: 2.5,
              ),
            ),
            filled: true,
            fillColor: RevolutionaryColors.jasmineWhite,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 18,
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'كلمة المرور مطلوبة';
            }
            return null;
          },
          onFieldSubmitted: (_) => _handleLogin(),
        ),
      ),
    );
  }

  Widget _buildRevolutionaryLoginButton() {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        gradient: _isLoading || _failedAttempts >= _maxFailedAttempts
            ? LinearGradient(
                colors: [
                  RevolutionaryColors.textHint,
                  RevolutionaryColors.textSecondary,
                ],
              )
            : RevolutionaryColors.damascusGradient,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          if (!_isLoading && _failedAttempts < _maxFailedAttempts)
            RevolutionaryColors.createGlowShadow(
              color: RevolutionaryColors.damascusSky,
              blurRadius: 15,
              spreadRadius: 3,
              offset: const Offset(0, 6),
            ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading || _failedAttempts >= _maxFailedAttempts
            ? null
            : _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 28,
                width: 28,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    RevolutionaryColors.jasmineWhite,
                  ),
                ),
              )
            : Text(
                _failedAttempts >= _maxFailedAttempts
                    ? 'تم حظر المحاولات'
                    : 'تسجيل الدخول',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.textOnDark,
                  letterSpacing: 1.2,
                  fontFamily: 'Cairo',
                ),
              ),
      ),
    );
  }

  Widget _buildRevolutionaryErrorMessage() {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            _shakeAnimation.value * 12 * (1 - _shakeAnimation.value),
            0,
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                  RevolutionaryColors.errorCoral.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(18),
              border: Border.all(
                color: RevolutionaryColors.errorCoral.withValues(alpha: 0.4),
                width: 1.5,
              ),
              boxShadow: [
                RevolutionaryColors.createGlowShadow(
                  color: RevolutionaryColors.errorCoral,
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: RevolutionaryColors.errorCoral,
                  size: 26,
                ),
                const SizedBox(width: 14),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(
                      color: RevolutionaryColors.errorCoral,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRevolutionaryAdditionalOptions() {
    return Column(
      children: [
        if (_failedAttempts > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  RevolutionaryColors.warningAmber.withValues(alpha: 0.1),
                  RevolutionaryColors.warningAmber.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: RevolutionaryColors.warningAmber.withValues(alpha: 0.4),
                width: 1.5,
              ),
            ),
            child: Text(
              'المحاولات المتبقية: ${_maxFailedAttempts - _failedAttempts}',
              style: TextStyle(
                color: RevolutionaryColors.warningAmber,
                fontWeight: FontWeight.bold,
                fontSize: 15,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
        ],

        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: RevolutionaryColors.damascusSky.withValues(alpha: 0.4),
              width: 1.5,
            ),
            gradient: LinearGradient(
              colors: [
                RevolutionaryColors.damascusSky.withValues(alpha: 0.05),
                RevolutionaryColors.syrianGold.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: TextButton(
            onPressed: _isLoading ? null : _showResetDialog,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: Text(
              'نسيت كلمة المرور؟',
              style: TextStyle(
                color: RevolutionaryColors.damascusSky,
                fontWeight: FontWeight.w700,
                fontSize: 16,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final password = _passwordController.text;

      // تهيئة قاعدة البيانات مع كلمة المرور
      final databaseHelper = DatabaseHelper();
      final success = await databaseHelper.initializeDatabase(password);

      if (success) {
        LoggingService.security('تم تسجيل الدخول بنجاح', category: 'Login');

        if (mounted) {
          Navigator.of(context).pushReplacement(
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  const RevolutionaryHomeScreen(),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: SlideTransition(
                        position:
                            Tween<Offset>(
                              begin: const Offset(1.0, 0.0),
                              end: Offset.zero,
                            ).animate(
                              CurvedAnimation(
                                parent: animation,
                                curve: Curves.easeInOut,
                              ),
                            ),
                        child: child,
                      ),
                    );
                  },
              transitionDuration: const Duration(milliseconds: 800),
            ),
          );
        }
      } else {
        _failedAttempts++;
        LoggingService.security(
          'فشل في تسجيل الدخول - المحاولة $_failedAttempts',
          category: 'Login',
        );

        setState(() {
          if (_failedAttempts >= _maxFailedAttempts) {
            _errorMessage = 'تم حظر المحاولات بسبب كثرة المحاولات الفاشلة';
          } else {
            _errorMessage =
                'كلمة مرور خاطئة. المحاولات المتبقية: ${_maxFailedAttempts - _failedAttempts}';
          }
        });

        // تأثير الاهتزاز عند الخطأ
        _shakeController.forward().then((_) {
          _shakeController.reset();
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تسجيل الدخول: ${e.toString()}';
      });

      // تأثير الاهتزاز عند الخطأ
      _shakeController.forward().then((_) {
        _shakeController.reset();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(
          'إعادة تعيين كلمة المرور',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.textPrimary,
          ),
        ),
        content: Text(
          'تحذير: إعادة تعيين كلمة المرور سيؤدي إلى فقدان جميع البيانات المشفرة.\n\nهل أنت متأكد من المتابعة؟',
          style: TextStyle(
            fontFamily: 'Cairo',
            color: RevolutionaryColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: TextStyle(
                fontFamily: 'Cairo',
                color: RevolutionaryColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _resetEncryption();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(
                color: RevolutionaryColors.textOnDark,
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _resetEncryption() async {
    final success = await EncryptionService.resetEncryption();
    if (success && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const PasswordSetupScreen(isFirstTime: true),
        ),
      );
    }
  }
}
