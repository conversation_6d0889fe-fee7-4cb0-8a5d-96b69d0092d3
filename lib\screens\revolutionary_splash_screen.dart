import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/app_logo.dart';
import '../services/advanced_features_initialization_service.dart';
import 'revolutionary_login_screen.dart';

/// شاشة البداية الثورية مع تصميم سوري أصيل وتأثيرات بصرية مذهلة
class RevolutionarySplashScreen extends StatefulWidget {
  const RevolutionarySplashScreen({super.key});

  @override
  State<RevolutionarySplashScreen> createState() =>
      _RevolutionarySplashScreenState();
}

class _RevolutionarySplashScreenState extends State<RevolutionarySplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _logoController;
  late AnimationController _particleController;
  late AnimationController _textController;
  late AnimationController _backgroundController;
  late AnimationController _glowController;

  late Animation<double> _logoScale;
  late Animation<double> _logoRotation;
  late Animation<double> _logoOpacity;
  late Animation<Offset> _logoSlide;

  late Animation<double> _titleOpacity;
  late Animation<Offset> _titleSlide;
  late Animation<double> _subtitleOpacity;
  late Animation<Offset> _subtitleSlide;

  late Animation<double> _backgroundOpacity;
  late Animation<double> _particleAnimation;
  late Animation<double> _glowAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // التحكم الرئيسي
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 3500),
      vsync: this,
    );

    // تحكم الشعار
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // تحكم الجسيمات
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 5000),
      vsync: this,
    );

    // تحكم النصوص
    _textController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // تحكم الخلفية
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    // تحكم الوهج
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // تحريك الشعار
    _logoScale = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.7, curve: Curves.elasticOut),
      ),
    );

    _logoRotation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.9, curve: Curves.easeInOut),
      ),
    );

    _logoOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _logoSlide = Tween<Offset>(begin: const Offset(0, -3), end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _logoController,
            curve: const Interval(0.2, 0.9, curve: Curves.bounceOut),
          ),
        );

    // تحريك النصوص
    _titleOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );

    _titleSlide = Tween<Offset>(begin: const Offset(-2, 0), end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _textController,
            curve: const Interval(0.0, 0.7, curve: Curves.easeOutBack),
          ),
        );

    _subtitleOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.4, 1.0, curve: Curves.easeIn),
      ),
    );

    _subtitleSlide = Tween<Offset>(begin: const Offset(2, 0), end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _textController,
            curve: const Interval(0.4, 1.0, curve: Curves.easeOutBack),
          ),
        );

    // تحريك الخلفية والتأثيرات
    _backgroundOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _backgroundController, curve: Curves.easeInOut),
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    _colorAnimation =
        ColorTween(
          begin: RevolutionaryColors.damascusSky,
          end: RevolutionaryColors.syrianGold,
        ).animate(
          CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
        );
  }

  void _startAnimationSequence() async {
    // بدء تحريك الخلفية
    _backgroundController.forward();

    await Future.delayed(const Duration(milliseconds: 400));

    // بدء تحريك الجسيمات
    _particleController.repeat();

    // بدء الوهج
    _glowController.repeat(reverse: true);

    await Future.delayed(const Duration(milliseconds: 300));

    // بدء تحريك الشعار
    _logoController.forward();

    await Future.delayed(const Duration(milliseconds: 1000));

    // بدء تحريك النصوص
    _textController.forward();

    // بدء التحكم الرئيسي
    _mainController.forward();

    // تهيئة الميزات المتقدمة في الخلفية
    _initializeAdvancedFeatures();

    // الانتقال للشاشة التالية
    await Future.delayed(const Duration(milliseconds: 3000));

    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const RevolutionaryLoginScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: SlideTransition(
                position:
                    Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOut,
                      ),
                    ),
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 1000),
        ),
      );
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _logoController.dispose();
    _particleController.dispose();
    _textController.dispose();
    _backgroundController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  /// تهيئة الميزات المتقدمة في الخلفية
  void _initializeAdvancedFeatures() async {
    try {
      final initService = AdvancedFeaturesInitializationService();
      await initService.initializeAllFeatures();
      debugPrint('تم تهيئة الميزات المتقدمة بنجاح');
    } catch (e) {
      // تسجيل الخطأ دون إيقاف التطبيق
      debugPrint('تحذير: فشل في تهيئة بعض الميزات المتقدمة: $e');
      debugPrint('سيتم تشغيل التطبيق بالميزات الأساسية');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _mainController,
          _logoController,
          _particleController,
          _textController,
          _backgroundController,
          _glowController,
        ]),
        builder: (context, child) {
          return Container(
            decoration: const BoxDecoration(
              gradient: RevolutionaryColors.jasmineGradient,
            ),
            child: Stack(
              children: [
                // طبقة الخلفية المتدرجة
                _buildRevolutionaryBackground(),

                // جسيمات متحركة في الخلفية
                _buildRevolutionaryParticles(),

                // المحتوى الرئيسي
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // الشعار الثوري المتحرك
                      _buildRevolutionaryLogo(),

                      const SizedBox(height: 50),

                      // العنوان الثوري المتحرك
                      _buildRevolutionaryTitle(),

                      const SizedBox(height: 20),

                      // العنوان الفرعي الثوري المتحرك
                      _buildRevolutionarySubtitle(),

                      const SizedBox(height: 80),

                      // مؤشر التحميل الثوري المتحرك
                      _buildRevolutionaryLoadingIndicator(),
                    ],
                  ),
                ),

                // تأثير الوهج الثوري
                _buildRevolutionaryGlowEffect(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildRevolutionaryBackground() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _backgroundOpacity,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  RevolutionaryColors.damascusSky.withValues(
                    alpha: _backgroundOpacity.value * 0.8,
                  ),
                  RevolutionaryColors.syrianGold.withValues(
                    alpha: _backgroundOpacity.value * 0.6,
                  ),
                  RevolutionaryColors.jasmineWhite.withValues(
                    alpha: _backgroundOpacity.value,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRevolutionaryParticles() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _RevolutionaryParticlesPainter(_particleAnimation.value),
      ),
    );
  }

  Widget _buildRevolutionaryLogo() {
    return SlideTransition(
      position: _logoSlide,
      child: FadeTransition(
        opacity: _logoOpacity,
        child: Transform.rotate(
          angle: _logoRotation.value,
          child: Transform.scale(
            scale: _logoScale.value,
            child: Container(
              width: 140,
              height: 140,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RevolutionaryColors.damascusGradient,
                boxShadow: [
                  RevolutionaryColors.createGlowShadow(
                    color:
                        _colorAnimation.value ??
                        RevolutionaryColors.damascusSky,
                    blurRadius: 35 * _glowAnimation.value,
                    spreadRadius: 12 * _glowAnimation.value,
                  ),
                  RevolutionaryColors.createGlowShadow(
                    color: RevolutionaryColors.syrianGold,
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Center(child: AppLogo(size: 85)),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRevolutionaryTitle() {
    return SlideTransition(
      position: _titleSlide,
      child: FadeTransition(
        opacity: _titleOpacity,
        child: ShaderMask(
          shaderCallback: (bounds) =>
              RevolutionaryColors.goldGradient.createShader(bounds),
          child: Text(
            AppConstants.appNameArabic,
            style: const TextStyle(
              fontSize: 38,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 2.5,
              fontFamily: 'Cairo',
              shadows: [
                Shadow(
                  color: RevolutionaryColors.shadowMedium,
                  offset: Offset(3, 3),
                  blurRadius: 6,
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildRevolutionarySubtitle() {
    return SlideTransition(
      position: _subtitleSlide,
      child: FadeTransition(
        opacity: _subtitleOpacity,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
                RevolutionaryColors.syrianGold.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: RevolutionaryColors.syrianGold.withValues(alpha: 0.3),
              width: 1.5,
            ),
          ),
          child: Text(
            'نظام محاسبة ثوري ومتكامل للشركات السورية',
            style: TextStyle(
              fontSize: 16,
              color: RevolutionaryColors.textPrimary,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildRevolutionaryLoadingIndicator() {
    return FadeTransition(
      opacity: _backgroundOpacity,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RevolutionaryColors.damascusGradient,
          boxShadow: [
            RevolutionaryColors.createGlowShadow(
              color: RevolutionaryColors.damascusSky,
              blurRadius: 15,
              spreadRadius: 3,
            ),
          ],
        ),
        child: const Padding(
          padding: EdgeInsets.all(12),
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              RevolutionaryColors.jasmineWhite,
            ),
            strokeWidth: 3,
          ),
        ),
      ),
    );
  }

  Widget _buildRevolutionaryGlowEffect() {
    return Positioned.fill(
      child: IgnorePointer(
        child: Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.2,
              colors: [
                (_colorAnimation.value ?? RevolutionaryColors.damascusSky)
                    .withValues(alpha: _glowAnimation.value * 0.15),
                RevolutionaryColors.syrianGold.withValues(
                  alpha: _glowAnimation.value * 0.1,
                ),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// رسام الجسيمات الثورية المتحركة
class _RevolutionaryParticlesPainter extends CustomPainter {
  final double animationValue;
  final List<_RevolutionaryParticle> particles = [];

  _RevolutionaryParticlesPainter(this.animationValue) {
    // إنشاء جسيمات ثورية عشوائية
    for (int i = 0; i < 60; i++) {
      particles.add(_RevolutionaryParticle());
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      final x = particle.x * size.width;
      final y =
          (particle.y + animationValue * particle.speed) % 1.0 * size.height;
      final radius =
          particle.size *
          (1 + math.sin(animationValue * 2 * math.pi + particle.phase) * 0.5);

      // استخدام ألوان ثورية مختلفة للجسيمات
      Color particleColor;
      switch (particle.colorType) {
        case 0:
          particleColor = RevolutionaryColors.syrianGold;
          break;
        case 1:
          particleColor = RevolutionaryColors.damascusSky;
          break;
        case 2:
          particleColor = RevolutionaryColors.jasmineWhite;
          break;
        default:
          particleColor = RevolutionaryColors.oliveBranch;
      }

      canvas.drawCircle(
        Offset(x, y),
        radius,
        paint..color = particleColor.withValues(alpha: particle.opacity * 0.7),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// فئة الجسيمة الثورية
class _RevolutionaryParticle {
  final double x;
  final double y;
  final double size;
  final double speed;
  final double opacity;
  final double phase;
  final int colorType;

  _RevolutionaryParticle()
    : x = math.Random().nextDouble(),
      y = math.Random().nextDouble(),
      size = math.Random().nextDouble() * 4 + 1,
      speed = math.Random().nextDouble() * 0.3 + 0.1,
      opacity = math.Random().nextDouble() * 0.8 + 0.2,
      phase = math.Random().nextDouble() * 2 * math.pi,
      colorType = math.Random().nextInt(4);
}
