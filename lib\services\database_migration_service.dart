/// خدمة ترحيل قاعدة البيانات
/// توفر طرق للتحقق من الأعمدة المفقودة وإضافتها تلقائياً
library;

import 'package:sqflite_sqlcipher/sqflite.dart';
import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';

class DatabaseMigrationService {
  static final DatabaseMigrationService _instance =
      DatabaseMigrationService._internal();
  factory DatabaseMigrationService() => _instance;
  DatabaseMigrationService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// التحقق من جميع الأعمدة المطلوبة وإضافة المفقود منها
  Future<void> ensureAllRequiredColumnsExist() async {
    try {
      final db = await _databaseHelper.database;

      LoggingService.info(
        'بدء التحقق من الأعمدة المطلوبة في قاعدة البيانات',
        category: 'DatabaseMigration',
      );

      // التحقق من أعمدة جدول الفواتير
      await _ensureInvoiceColumnsExist(db);

      // التحقق من أعمدة جدول القيود اليومية
      await _ensureJournalEntryColumnsExist(db);

      // التحقق من أعمدة جدول الحسابات
      await _ensureAccountColumnsExist(db);

      // التحقق من وجود جدول المدفوعات
      await _ensurePaymentsTableExists(db);

      // التحقق من أعمدة جدول مواقع الأصناف
      await _ensureItemLocationsColumnsExist(db);

      // التحقق من جداول الموارد البشرية
      await _ensureHRTablesExist(db);

      // التحقق من جداول الرواتب
      await _ensurePayrollTablesExist(db);

      LoggingService.info(
        'تم الانتهاء من التحقق من جميع الأعمدة المطلوبة',
        category: 'DatabaseMigration',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من الأعمدة المطلوبة',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من أعمدة جدول الفواتير
  Future<void> _ensureInvoiceColumnsExist(Database db) async {
    final requiredColumns = {
      'invoice_type': 'TEXT DEFAULT "sale"',
      'due_date': 'TEXT',
      'terms': 'TEXT',
      'reference': 'TEXT',
    };

    await _ensureColumnsExist(db, AppConstants.invoicesTable, requiredColumns);
  }

  /// التحقق من أعمدة جدول القيود اليومية
  Future<void> _ensureJournalEntryColumnsExist(Database db) async {
    final requiredColumns = {
      'reference': 'TEXT',
      'reference_type': 'TEXT',
      'reference_id': 'INTEGER',
    };

    await _ensureColumnsExist(
      db,
      AppConstants.journalEntriesTable,
      requiredColumns,
    );
  }

  /// التحقق من أعمدة جدول الحسابات
  Future<void> _ensureAccountColumnsExist(Database db) async {
    final requiredColumns = {
      'opening_balance': 'REAL DEFAULT 0.0',
      'balance': 'REAL DEFAULT 0.0',
      'account_type': 'TEXT',
    };

    await _ensureColumnsExist(db, AppConstants.accountsTable, requiredColumns);
  }

  /// التحقق من وجود جدول المدفوعات وإنشاؤه إذا لم يكن موجوداً
  Future<void> _ensurePaymentsTableExists(Database db) async {
    try {
      final tableExists = await this.tableExists(AppConstants.paymentsTable);

      if (!tableExists) {
        LoggingService.info(
          'إنشاء جدول المدفوعات المفقود',
          category: 'DatabaseMigration',
        );

        await db.execute('''
          CREATE TABLE ${AppConstants.paymentsTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            method TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'pending',
            payment_date TEXT NOT NULL,
            reference TEXT,
            bank_name TEXT,
            account_number TEXT,
            notes TEXT,
            user_id TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE
          )
        ''');

        // إنشاء فهارس للجدول
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON ${AppConstants.paymentsTable}(invoice_id)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_status ON ${AppConstants.paymentsTable}(status)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_date ON ${AppConstants.paymentsTable}(payment_date)',
        );

        LoggingService.info(
          'تم إنشاء جدول المدفوعات بنجاح',
          category: 'DatabaseMigration',
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء جدول المدفوعات',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
    }
  }

  /// التحقق من وجود أعمدة معينة في جدول وإضافة المفقود منها
  Future<void> _ensureColumnsExist(
    Database db,
    String tableName,
    Map<String, String> requiredColumns,
  ) async {
    try {
      // الحصول على معلومات الجدول
      final tableInfo = await db.rawQuery('PRAGMA table_info($tableName)');

      if (tableInfo.isEmpty) {
        LoggingService.warning(
          'الجدول غير موجود: $tableName',
          category: 'DatabaseMigration',
        );
        return;
      }

      // الحصول على أسماء الأعمدة الموجودة
      final existingColumns = tableInfo
          .map((row) => row['name'] as String)
          .toSet();

      // التحقق من كل عمود مطلوب
      for (final entry in requiredColumns.entries) {
        final columnName = entry.key;
        final columnDefinition = entry.value;

        if (!existingColumns.contains(columnName)) {
          LoggingService.info(
            'إضافة عمود مفقود: $columnName إلى جدول $tableName',
            category: 'DatabaseMigration',
          );

          try {
            await db.execute(
              'ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition',
            );

            LoggingService.info(
              'تم إضافة العمود بنجاح: $columnName',
              category: 'DatabaseMigration',
            );
          } catch (e) {
            LoggingService.error(
              'خطأ في إضافة العمود: $columnName',
              category: 'DatabaseMigration',
              data: {
                'table': tableName,
                'column': columnName,
                'definition': columnDefinition,
                'error': e.toString(),
              },
            );
          }
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من أعمدة الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {'table': tableName, 'error': e.toString()},
      );
    }
  }

  /// التحقق من وجود جدول معين
  Future<bool> tableExists(String tableName) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName],
      );
      return result.isNotEmpty;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من وجود الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {'table': tableName, 'error': e.toString()},
      );
      return false;
    }
  }

  /// التحقق من وجود عمود معين في جدول
  Future<bool> columnExists(String tableName, String columnName) async {
    try {
      final db = await _databaseHelper.database;
      final tableInfo = await db.rawQuery('PRAGMA table_info($tableName)');

      for (final row in tableInfo) {
        if (row['name'] == columnName) {
          return true;
        }
      }
      return false;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من وجود العمود: $columnName في الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {'table': tableName, 'column': columnName, 'error': e.toString()},
      );
      return false;
    }
  }

  /// إضافة عمود جديد إلى جدول
  Future<bool> addColumn(
    String tableName,
    String columnName,
    String columnDefinition,
  ) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود العمود مسبقاً
      if (await columnExists(tableName, columnName)) {
        LoggingService.info(
          'العمود موجود مسبقاً: $columnName في الجدول: $tableName',
          category: 'DatabaseMigration',
        );
        return true;
      }

      await db.execute(
        'ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition',
      );

      LoggingService.info(
        'تم إضافة العمود بنجاح: $columnName إلى الجدول: $tableName',
        category: 'DatabaseMigration',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة العمود: $columnName إلى الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {
          'table': tableName,
          'column': columnName,
          'definition': columnDefinition,
          'error': e.toString(),
        },
      );
      return false;
    }
  }

  /// التحقق من أعمدة جدول مواقع الأصناف
  Future<void> _ensureItemLocationsColumnsExist(Database db) async {
    final requiredColumns = {
      'available_quantity': 'REAL NOT NULL DEFAULT 0',
      'min_stock_level': 'REAL DEFAULT 0',
      'max_stock_level': 'REAL DEFAULT 0',
      'reorder_point': 'REAL DEFAULT 0',
      'last_movement_date': 'TEXT',
    };

    for (final entry in requiredColumns.entries) {
      final columnName = entry.key;
      final columnDefinition = entry.value;

      final exists = await columnExists(
        AppConstants.itemLocationsTable,
        columnName,
      );
      if (!exists) {
        await addColumn(
          AppConstants.itemLocationsTable,
          columnName,
          columnDefinition,
        );

        // إذا كان العمود available_quantity، احسب القيمة من quantity - reserved_quantity
        if (columnName == 'available_quantity') {
          await db.execute('''
            UPDATE ${AppConstants.itemLocationsTable}
            SET available_quantity = COALESCE(quantity, 0) - COALESCE(reserved_quantity, 0)
          ''');

          // إنشاء trigger لتحديث available_quantity تلقائياً
          await db.execute('''
            CREATE TRIGGER IF NOT EXISTS update_available_quantity
            AFTER UPDATE OF quantity, reserved_quantity ON ${AppConstants.itemLocationsTable}
            BEGIN
              UPDATE ${AppConstants.itemLocationsTable}
              SET available_quantity = COALESCE(NEW.quantity, 0) - COALESCE(NEW.reserved_quantity, 0)
              WHERE id = NEW.id;
            END;
          ''');
        }
      }
    }
  }

  /// التحقق من وجود جداول الموارد البشرية
  Future<void> _ensureHRTablesExist(Database db) async {
    try {
      // التحقق من جدول الحضور
      final attendanceExists = await tableExists(AppConstants.attendanceTable);
      if (!attendanceExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.attendanceTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            attendance_date TEXT NOT NULL,
            check_in_time TEXT,
            check_out_time TEXT,
            break_start_time TEXT,
            break_end_time TEXT,
            total_hours REAL NOT NULL DEFAULT 0,
            regular_hours REAL NOT NULL DEFAULT 0,
            overtime_hours REAL NOT NULL DEFAULT 0,
            late_minutes INTEGER NOT NULL DEFAULT 0,
            early_leave_minutes INTEGER NOT NULL DEFAULT 0,
            status TEXT NOT NULL DEFAULT 'present',
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
            UNIQUE(employee_id, attendance_date)
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول الحضور',
          category: 'DatabaseMigration',
        );
      }

      // التحقق من جدول الإجازات
      final leavesExists = await tableExists(AppConstants.leavesTable);
      if (!leavesExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.leavesTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            leave_type TEXT NOT NULL DEFAULT 'annual',
            start_date TEXT NOT NULL,
            end_date TEXT NOT NULL,
            total_days INTEGER NOT NULL,
            reason TEXT,
            status TEXT NOT NULL DEFAULT 'pending',
            requested_by INTEGER,
            approved_by INTEGER,
            approved_at TEXT,
            rejection_reason TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول الإجازات',
          category: 'DatabaseMigration',
        );
      }

      // التحقق من جدول القروض
      final loansExists = await tableExists(AppConstants.loansTable);
      if (!loansExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.loansTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            loan_number TEXT NOT NULL UNIQUE,
            amount REAL NOT NULL,
            interest_rate REAL NOT NULL DEFAULT 0,
            installments_count INTEGER NOT NULL,
            monthly_installment REAL NOT NULL,
            remaining_amount REAL NOT NULL,
            start_date TEXT NOT NULL,
            end_date TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'active',
            purpose TEXT,
            guarantor_id INTEGER,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
            FOREIGN KEY (guarantor_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE SET NULL
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول القروض',
          category: 'DatabaseMigration',
        );
      }

      // التحقق من جدول الأقسام
      final departmentsExists = await tableExists(
        AppConstants.departmentsTable,
      );
      if (!departmentsExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.departmentsTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            description TEXT,
            manager_id INTEGER,
            cost_center_account_id INTEGER,
            is_active INTEGER NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (manager_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE SET NULL,
            FOREIGN KEY (cost_center_account_id) REFERENCES ${AppConstants.accountsTable} (id) ON DELETE SET NULL
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول الأقسام',
          category: 'DatabaseMigration',
        );
      }

      // التحقق من جدول المناصب
      final positionsExists = await tableExists(AppConstants.positionsTable);
      if (!positionsExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.positionsTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT NOT NULL UNIQUE,
            title TEXT NOT NULL,
            description TEXT,
            department_id INTEGER,
            level INTEGER NOT NULL DEFAULT 1,
            min_salary REAL DEFAULT 0,
            max_salary REAL DEFAULT 0,
            is_active INTEGER NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (department_id) REFERENCES ${AppConstants.departmentsTable} (id) ON DELETE SET NULL
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول المناصب',
          category: 'DatabaseMigration',
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من جداول الموارد البشرية',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من وجود جداول الرواتب
  Future<void> _ensurePayrollTablesExist(Database db) async {
    try {
      // التحقق من جدول كشوف الرواتب
      final payrollExists = await tableExists(AppConstants.payrollTable);
      if (!payrollExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.payrollTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            payroll_number TEXT NOT NULL UNIQUE,
            month INTEGER NOT NULL,
            year INTEGER NOT NULL,
            pay_date TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'draft',
            total_basic_salary REAL NOT NULL DEFAULT 0,
            total_allowances REAL NOT NULL DEFAULT 0,
            total_deductions REAL NOT NULL DEFAULT 0,
            total_tax REAL NOT NULL DEFAULT 0,
            total_insurance REAL NOT NULL DEFAULT 0,
            total_net_salary REAL NOT NULL DEFAULT 0,
            notes TEXT,
            created_by INTEGER,
            approved_by INTEGER,
            approved_at TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (created_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL,
            FOREIGN KEY (approved_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول كشوف الرواتب',
          category: 'DatabaseMigration',
        );
      }

      // التحقق من جدول تفاصيل كشوف الرواتب
      final payrollDetailsExists = await tableExists(
        AppConstants.payrollDetailsTable,
      );
      if (!payrollDetailsExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.payrollDetailsTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            payroll_id INTEGER NOT NULL,
            employee_id INTEGER NOT NULL,
            basic_salary REAL NOT NULL DEFAULT 0,
            allowances REAL NOT NULL DEFAULT 0,
            overtime_amount REAL NOT NULL DEFAULT 0,
            bonus_amount REAL NOT NULL DEFAULT 0,
            deductions REAL NOT NULL DEFAULT 0,
            loan_deduction REAL NOT NULL DEFAULT 0,
            tax_amount REAL NOT NULL DEFAULT 0,
            insurance_amount REAL NOT NULL DEFAULT 0,
            net_salary REAL NOT NULL DEFAULT 0,
            working_days INTEGER NOT NULL DEFAULT 0,
            actual_working_days INTEGER NOT NULL DEFAULT 0,
            overtime_hours REAL NOT NULL DEFAULT 0,
            late_hours REAL NOT NULL DEFAULT 0,
            absence_days INTEGER NOT NULL DEFAULT 0,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (payroll_id) REFERENCES ${AppConstants.payrollTable} (id) ON DELETE CASCADE,
            FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول تفاصيل كشوف الرواتب',
          category: 'DatabaseMigration',
        );
      }

      // التحقق من جدول تفاصيل عناصر الراتب
      final salaryDetailsExists = await tableExists(
        AppConstants.salaryDetailsTable,
      );
      if (!salaryDetailsExists) {
        await db.execute('''
          CREATE TABLE ${AppConstants.salaryDetailsTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            component_type TEXT NOT NULL,
            component_name TEXT NOT NULL,
            amount REAL NOT NULL,
            is_percentage INTEGER NOT NULL DEFAULT 0,
            percentage_of TEXT,
            is_taxable INTEGER NOT NULL DEFAULT 1,
            is_active INTEGER NOT NULL DEFAULT 1,
            effective_from TEXT NOT NULL,
            effective_to TEXT,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول تفاصيل عناصر الراتب',
          category: 'DatabaseMigration',
        );
      }

      // التحقق من جدول سجلات الرواتب الفردية
      final payrollRecordsExists = await tableExists('payroll_records');
      if (!payrollRecordsExists) {
        await db.execute('''
          CREATE TABLE payroll_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            month INTEGER NOT NULL,
            year INTEGER NOT NULL,
            basic_salary REAL NOT NULL DEFAULT 0,
            allowances REAL NOT NULL DEFAULT 0,
            bonuses REAL NOT NULL DEFAULT 0,
            overtime_amount REAL NOT NULL DEFAULT 0,
            gross_salary REAL NOT NULL DEFAULT 0,
            income_tax REAL NOT NULL DEFAULT 0,
            social_insurance REAL NOT NULL DEFAULT 0,
            loan_deductions REAL NOT NULL DEFAULT 0,
            other_deductions REAL NOT NULL DEFAULT 0,
            total_deductions REAL NOT NULL DEFAULT 0,
            net_salary REAL NOT NULL DEFAULT 0,
            working_days INTEGER NOT NULL DEFAULT 0,
            actual_working_days INTEGER NOT NULL DEFAULT 0,
            overtime_hours REAL NOT NULL DEFAULT 0,
            absence_days INTEGER NOT NULL DEFAULT 0,
            status TEXT NOT NULL DEFAULT 'calculated',
            paid_date TEXT,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
            UNIQUE(employee_id, month, year)
          )
        ''');

        LoggingService.info(
          'تم إنشاء جدول سجلات الرواتب الفردية',
          category: 'DatabaseMigration',
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من جداول الرواتب',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إصلاح مشاكل قاعدة البيانات الشائعة
  Future<void> fixCommonDatabaseIssues() async {
    try {
      LoggingService.info(
        'بدء إصلاح مشاكل قاعدة البيانات الشائعة',
        category: 'DatabaseMigration',
      );

      // التحقق من جميع الأعمدة المطلوبة
      await ensureAllRequiredColumnsExist();

      // إضافة أي فهارس مفقودة
      await _ensureIndexesExist();

      LoggingService.info(
        'تم الانتهاء من إصلاح مشاكل قاعدة البيانات',
        category: 'DatabaseMigration',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إصلاح مشاكل قاعدة البيانات',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من وجود الفهارس المطلوبة
  Future<void> _ensureIndexesExist() async {
    try {
      final db = await _databaseHelper.database;

      // فهارس جدول الفواتير
      await _createIndexIfNotExists(
        db,
        'idx_invoices_type',
        AppConstants.invoicesTable,
        'invoice_type',
      );
      await _createIndexIfNotExists(
        db,
        'idx_invoices_date',
        AppConstants.invoicesTable,
        'invoice_date',
      );

      // فهارس جدول القيود
      await _createIndexIfNotExists(
        db,
        'idx_journal_entries_reference',
        AppConstants.journalEntriesTable,
        'reference',
      );

      // فهارس جدول الحسابات
      await _createIndexIfNotExists(
        db,
        'idx_accounts_code',
        AppConstants.accountsTable,
        'code',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الفهارس',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء فهرس إذا لم يكن موجوداً
  Future<void> _createIndexIfNotExists(
    Database db,
    String indexName,
    String tableName,
    String columnName,
  ) async {
    try {
      await db.execute(
        'CREATE INDEX IF NOT EXISTS $indexName ON $tableName($columnName)',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الفهرس: $indexName',
        category: 'DatabaseMigration',
        data: {
          'index': indexName,
          'table': tableName,
          'column': columnName,
          'error': e.toString(),
        },
      );
    }
  }
}
