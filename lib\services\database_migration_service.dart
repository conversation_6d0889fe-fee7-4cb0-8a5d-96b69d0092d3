/// خدمة ترحيل قاعدة البيانات
/// توفر طرق للتحقق من الأعمدة المفقودة وإضافتها تلقائياً
library;

import 'package:sqflite_sqlcipher/sqflite.dart';
import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';

class DatabaseMigrationService {
  static final DatabaseMigrationService _instance =
      DatabaseMigrationService._internal();
  factory DatabaseMigrationService() => _instance;
  DatabaseMigrationService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// التحقق من جميع الأعمدة المطلوبة وإضافة المفقود منها
  Future<void> ensureAllRequiredColumnsExist() async {
    try {
      final db = await _databaseHelper.database;

      LoggingService.info(
        'بدء التحقق من الأعمدة المطلوبة في قاعدة البيانات',
        category: 'DatabaseMigration',
      );

      // التحقق من أعمدة جدول الفواتير
      await _ensureInvoiceColumnsExist(db);

      // التحقق من أعمدة جدول القيود اليومية
      await _ensureJournalEntryColumnsExist(db);

      // التحقق من أعمدة جدول الحسابات
      await _ensureAccountColumnsExist(db);

      // التحقق من وجود جدول المدفوعات
      await _ensurePaymentsTableExists(db);

      // التحقق من أعمدة جدول مواقع الأصناف
      await _ensureItemLocationsColumnsExist(db);

      LoggingService.info(
        'تم الانتهاء من التحقق من جميع الأعمدة المطلوبة',
        category: 'DatabaseMigration',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من الأعمدة المطلوبة',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من أعمدة جدول الفواتير
  Future<void> _ensureInvoiceColumnsExist(Database db) async {
    final requiredColumns = {
      'invoice_type': 'TEXT DEFAULT "sale"',
      'due_date': 'TEXT',
      'terms': 'TEXT',
      'reference': 'TEXT',
    };

    await _ensureColumnsExist(db, AppConstants.invoicesTable, requiredColumns);
  }

  /// التحقق من أعمدة جدول القيود اليومية
  Future<void> _ensureJournalEntryColumnsExist(Database db) async {
    final requiredColumns = {
      'reference': 'TEXT',
      'reference_type': 'TEXT',
      'reference_id': 'INTEGER',
    };

    await _ensureColumnsExist(
      db,
      AppConstants.journalEntriesTable,
      requiredColumns,
    );
  }

  /// التحقق من أعمدة جدول الحسابات
  Future<void> _ensureAccountColumnsExist(Database db) async {
    final requiredColumns = {
      'opening_balance': 'REAL DEFAULT 0.0',
      'balance': 'REAL DEFAULT 0.0',
      'account_type': 'TEXT',
    };

    await _ensureColumnsExist(db, AppConstants.accountsTable, requiredColumns);
  }

  /// التحقق من وجود جدول المدفوعات وإنشاؤه إذا لم يكن موجوداً
  Future<void> _ensurePaymentsTableExists(Database db) async {
    try {
      final tableExists = await this.tableExists(AppConstants.paymentsTable);

      if (!tableExists) {
        LoggingService.info(
          'إنشاء جدول المدفوعات المفقود',
          category: 'DatabaseMigration',
        );

        await db.execute('''
          CREATE TABLE ${AppConstants.paymentsTable} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            method TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'pending',
            payment_date TEXT NOT NULL,
            reference TEXT,
            bank_name TEXT,
            account_number TEXT,
            notes TEXT,
            user_id TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE
          )
        ''');

        // إنشاء فهارس للجدول
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON ${AppConstants.paymentsTable}(invoice_id)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_status ON ${AppConstants.paymentsTable}(status)',
        );
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_payments_date ON ${AppConstants.paymentsTable}(payment_date)',
        );

        LoggingService.info(
          'تم إنشاء جدول المدفوعات بنجاح',
          category: 'DatabaseMigration',
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء جدول المدفوعات',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
    }
  }

  /// التحقق من وجود أعمدة معينة في جدول وإضافة المفقود منها
  Future<void> _ensureColumnsExist(
    Database db,
    String tableName,
    Map<String, String> requiredColumns,
  ) async {
    try {
      // الحصول على معلومات الجدول
      final tableInfo = await db.rawQuery('PRAGMA table_info($tableName)');

      if (tableInfo.isEmpty) {
        LoggingService.warning(
          'الجدول غير موجود: $tableName',
          category: 'DatabaseMigration',
        );
        return;
      }

      // الحصول على أسماء الأعمدة الموجودة
      final existingColumns = tableInfo
          .map((row) => row['name'] as String)
          .toSet();

      // التحقق من كل عمود مطلوب
      for (final entry in requiredColumns.entries) {
        final columnName = entry.key;
        final columnDefinition = entry.value;

        if (!existingColumns.contains(columnName)) {
          LoggingService.info(
            'إضافة عمود مفقود: $columnName إلى جدول $tableName',
            category: 'DatabaseMigration',
          );

          try {
            await db.execute(
              'ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition',
            );

            LoggingService.info(
              'تم إضافة العمود بنجاح: $columnName',
              category: 'DatabaseMigration',
            );
          } catch (e) {
            LoggingService.error(
              'خطأ في إضافة العمود: $columnName',
              category: 'DatabaseMigration',
              data: {
                'table': tableName,
                'column': columnName,
                'definition': columnDefinition,
                'error': e.toString(),
              },
            );
          }
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من أعمدة الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {'table': tableName, 'error': e.toString()},
      );
    }
  }

  /// التحقق من وجود جدول معين
  Future<bool> tableExists(String tableName) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName],
      );
      return result.isNotEmpty;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من وجود الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {'table': tableName, 'error': e.toString()},
      );
      return false;
    }
  }

  /// التحقق من وجود عمود معين في جدول
  Future<bool> columnExists(String tableName, String columnName) async {
    try {
      final db = await _databaseHelper.database;
      final tableInfo = await db.rawQuery('PRAGMA table_info($tableName)');

      for (final row in tableInfo) {
        if (row['name'] == columnName) {
          return true;
        }
      }
      return false;
    } catch (e) {
      LoggingService.error(
        'خطأ في التحقق من وجود العمود: $columnName في الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {'table': tableName, 'column': columnName, 'error': e.toString()},
      );
      return false;
    }
  }

  /// إضافة عمود جديد إلى جدول
  Future<bool> addColumn(
    String tableName,
    String columnName,
    String columnDefinition,
  ) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم وجود العمود مسبقاً
      if (await columnExists(tableName, columnName)) {
        LoggingService.info(
          'العمود موجود مسبقاً: $columnName في الجدول: $tableName',
          category: 'DatabaseMigration',
        );
        return true;
      }

      await db.execute(
        'ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition',
      );

      LoggingService.info(
        'تم إضافة العمود بنجاح: $columnName إلى الجدول: $tableName',
        category: 'DatabaseMigration',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة العمود: $columnName إلى الجدول: $tableName',
        category: 'DatabaseMigration',
        data: {
          'table': tableName,
          'column': columnName,
          'definition': columnDefinition,
          'error': e.toString(),
        },
      );
      return false;
    }
  }

  /// التحقق من أعمدة جدول مواقع الأصناف
  Future<void> _ensureItemLocationsColumnsExist(Database db) async {
    final requiredColumns = {
      'available_quantity': 'REAL NOT NULL DEFAULT 0',
      'min_stock_level': 'REAL DEFAULT 0',
      'max_stock_level': 'REAL DEFAULT 0',
      'reorder_point': 'REAL DEFAULT 0',
      'last_movement_date': 'TEXT',
    };

    for (final entry in requiredColumns.entries) {
      final columnName = entry.key;
      final columnDefinition = entry.value;

      final exists = await columnExists(
        AppConstants.itemLocationsTable,
        columnName,
      );
      if (!exists) {
        await addColumn(
          AppConstants.itemLocationsTable,
          columnName,
          columnDefinition,
        );

        // إذا كان العمود available_quantity، احسب القيمة من quantity - reserved_quantity
        if (columnName == 'available_quantity') {
          await db.execute('''
            UPDATE ${AppConstants.itemLocationsTable}
            SET available_quantity = COALESCE(quantity, 0) - COALESCE(reserved_quantity, 0)
          ''');

          // إنشاء trigger لتحديث available_quantity تلقائياً
          await db.execute('''
            CREATE TRIGGER IF NOT EXISTS update_available_quantity
            AFTER UPDATE OF quantity, reserved_quantity ON ${AppConstants.itemLocationsTable}
            BEGIN
              UPDATE ${AppConstants.itemLocationsTable}
              SET available_quantity = COALESCE(NEW.quantity, 0) - COALESCE(NEW.reserved_quantity, 0)
              WHERE id = NEW.id;
            END;
          ''');
        }
      }
    }
  }

  /// إصلاح مشاكل قاعدة البيانات الشائعة
  Future<void> fixCommonDatabaseIssues() async {
    try {
      LoggingService.info(
        'بدء إصلاح مشاكل قاعدة البيانات الشائعة',
        category: 'DatabaseMigration',
      );

      // التحقق من جميع الأعمدة المطلوبة
      await ensureAllRequiredColumnsExist();

      // إضافة أي فهارس مفقودة
      await _ensureIndexesExist();

      LoggingService.info(
        'تم الانتهاء من إصلاح مشاكل قاعدة البيانات',
        category: 'DatabaseMigration',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إصلاح مشاكل قاعدة البيانات',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من وجود الفهارس المطلوبة
  Future<void> _ensureIndexesExist() async {
    try {
      final db = await _databaseHelper.database;

      // فهارس جدول الفواتير
      await _createIndexIfNotExists(
        db,
        'idx_invoices_type',
        AppConstants.invoicesTable,
        'invoice_type',
      );
      await _createIndexIfNotExists(
        db,
        'idx_invoices_date',
        AppConstants.invoicesTable,
        'invoice_date',
      );

      // فهارس جدول القيود
      await _createIndexIfNotExists(
        db,
        'idx_journal_entries_reference',
        AppConstants.journalEntriesTable,
        'reference',
      );

      // فهارس جدول الحسابات
      await _createIndexIfNotExists(
        db,
        'idx_accounts_code',
        AppConstants.accountsTable,
        'code',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الفهارس',
        category: 'DatabaseMigration',
        data: {'error': e.toString()},
      );
    }
  }

  /// إنشاء فهرس إذا لم يكن موجوداً
  Future<void> _createIndexIfNotExists(
    Database db,
    String indexName,
    String tableName,
    String columnName,
  ) async {
    try {
      await db.execute(
        'CREATE INDEX IF NOT EXISTS $indexName ON $tableName($columnName)',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء الفهرس: $indexName',
        category: 'DatabaseMigration',
        data: {
          'index': indexName,
          'table': tableName,
          'column': columnName,
          'error': e.toString(),
        },
      );
    }
  }
}
