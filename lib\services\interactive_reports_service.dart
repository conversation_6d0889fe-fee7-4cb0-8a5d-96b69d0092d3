import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../models/report_models.dart';
import '../models/interactive_report_models.dart';
import '../services/enhanced_invoice_service.dart';
import '../services/audit_service.dart';
import 'logging_service.dart';

/// خدمة التقارير التفاعلية
/// توفر تقارير ديناميكية قابلة للتخصيص مع فلاتر متقدمة
class InteractiveReportsService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final EnhancedInvoiceService _enhancedInvoiceService =
      EnhancedInvoiceService();

  /// الحصول على تقرير تفاعلي مع فلاتر
  Future<InteractiveReportResult> getInteractiveReport({
    required String reportType,
    required ReportFilters filters,
    ReportConfiguration? configuration,
  }) async {
    try {
      LoggingService.info(
        'بدء إنشاء تقرير تفاعلي',
        category: 'InteractiveReports',
        data: {'reportType': reportType, 'filters': filters.toMap()},
      );

      switch (reportType) {
        case 'trial_balance':
          return await _getInteractiveTrialBalance(filters, configuration);
        case 'profit_loss':
          return await _getInteractiveProfitLoss(filters, configuration);
        case 'balance_sheet':
          return await _getInteractiveBalanceSheet(filters, configuration);
        case 'account_movements':
          return await _getInteractiveAccountMovements(filters, configuration);
        case 'customer_aging':
          return await _getInteractiveCustomerAging(filters, configuration);
        case 'supplier_aging':
          return await _getInteractiveSupplierAging(filters, configuration);
        case 'inventory_report':
          return await _getInteractiveInventoryReport(filters, configuration);
        case 'sales_analysis':
          return await _getInteractiveSalesAnalysis(filters, configuration);
        case 'purchase_analysis':
          return await _getInteractivePurchaseAnalysis(filters, configuration);
        case 'general_journal':
          return await _getInteractiveGeneralJournal(filters, configuration);
        case 'general_ledger':
          return await _getInteractiveGeneralLedger(filters, configuration);
        case 'tax_report':
          return await _getInteractiveTaxReport(filters, configuration);
        case 'custom_reports':
          return await _getInteractiveCustomReports(filters, configuration);
        case 'integration_status':
          return await _getIntegrationStatusReport(filters, configuration);
        case 'advanced_inventory':
          return await _getAdvancedInventoryReport(filters, configuration);
        case 'performance_analysis':
          return await _getPerformanceAnalysisReport(filters, configuration);
        case 'audit_log':
          return await _getAuditLogReport(filters, configuration);
        default:
          throw Exception('نوع التقرير غير مدعوم: $reportType');
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء التقرير التفاعلي',
        category: 'InteractiveReports',
        data: {'reportType': reportType, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تقرير ميزان المراجعة التفاعلي
  Future<InteractiveReportResult> _getInteractiveTrialBalance(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    // بناء الاستعلام الديناميكي
    String whereClause = 'WHERE a.is_active = 1';
    List<dynamic> params = [];

    if (filters.accountTypes.isNotEmpty) {
      whereClause +=
          ' AND a.type IN (${filters.accountTypes.map((_) => '?').join(',')})';
      params.addAll(filters.accountTypes);
    }

    if (filters.accountCodes.isNotEmpty) {
      whereClause +=
          ' AND a.code IN (${filters.accountCodes.map((_) => '?').join(',')})';
      params.addAll(filters.accountCodes);
    }

    String dateFilter = '';
    if (filters.fromDate != null && filters.toDate != null) {
      dateFilter = ' AND je.entry_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT 
        a.id,
        a.code,
        a.name,
        a.type,
        a.balance as opening_balance,
        COALESCE(SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0) as total_debit,
        COALESCE(SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0) as total_credit
      FROM ${AppConstants.accountsTable} a
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      $whereClause ${dateFilter.isNotEmpty ? 'AND (je.is_posted = 1 OR je.id IS NULL)$dateFilter' : 'AND (je.is_posted = 1 OR je.id IS NULL)'}
      GROUP BY a.id, a.code, a.name, a.type, a.balance
      ORDER BY ${_buildOrderByClause(config?.sortBy, config?.sortDirection)}
    ''';

    final result = await db.rawQuery(query, params);
    final data = result.map((row) => TrialBalanceItem.fromMap(row)).toList();

    // تطبيق فلاتر إضافية
    final filteredData = _applyAdditionalFilters(data, filters);

    return InteractiveReportResult(
      reportType: 'trial_balance',
      title: 'ميزان المراجعة التفاعلي',
      data: filteredData,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: filteredData.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0, // سيتم حسابه لاحقاً
      ),
    );
  }

  /// تقرير قائمة الدخل التفاعلي
  Future<InteractiveReportResult> _getInteractiveProfitLoss(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = 'WHERE a.is_active = 1 AND (a.type = ? OR a.type = ?)';
    List<dynamic> params = [
      AppConstants.accountTypeRevenue,
      AppConstants.accountTypeExpense,
    ];

    String dateFilter = '';
    if (filters.fromDate != null && filters.toDate != null) {
      dateFilter = ' AND je.entry_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT 
        a.type,
        a.id,
        a.code,
        a.name,
        COALESCE(SUM(jed.credit_amount - jed.debit_amount), 0) as net_amount
      FROM ${AppConstants.accountsTable} a
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      $whereClause ${dateFilter.isNotEmpty ? 'AND je.is_posted = 1$dateFilter' : 'AND (je.is_posted = 1 OR je.id IS NULL)'}
      GROUP BY a.type, a.id, a.code, a.name
      ORDER BY a.type, a.code
    ''';

    final result = await db.rawQuery(query, params);

    // تجميع البيانات حسب النوع
    double totalRevenue = 0;
    double totalExpense = 0;
    List<ProfitLossItem> revenueItems = [];
    List<ProfitLossItem> expenseItems = [];

    for (final row in result) {
      final type = row['type'] as String;
      final amount = (row['net_amount'] as num).toDouble();

      final item = ProfitLossItem(
        accountId: row['id'] as int,
        accountCode: row['code'] as String,
        accountName: row['name'] as String,
        amount: amount,
      );

      if (type == AppConstants.accountTypeRevenue) {
        totalRevenue += amount;
        revenueItems.add(item);
      } else if (type == AppConstants.accountTypeExpense) {
        totalExpense += amount.abs();
        expenseItems.add(item);
      }
    }

    final profitLossData = ProfitLossReportData(
      fromDate:
          filters.fromDate ?? DateTime.now().subtract(Duration(days: 365)),
      toDate: filters.toDate ?? DateTime.now(),
      revenueItems: revenueItems,
      expenseItems: expenseItems,
      totalRevenue: totalRevenue,
      totalExpense: totalExpense,
      netProfit: totalRevenue - totalExpense,
    );

    return InteractiveReportResult(
      reportType: 'profit_loss',
      title: 'قائمة الدخل التفاعلية',
      data: profitLossData,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: revenueItems.length + expenseItems.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
      ),
    );
  }

  /// تقرير الميزانية العمومية التفاعلي
  Future<InteractiveReportResult> _getInteractiveBalanceSheet(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    final asOfDate = filters.toDate ?? DateTime.now();

    final query =
        '''
      SELECT 
        a.type,
        a.id,
        a.code,
        a.name,
        a.balance + COALESCE(SUM(jed.debit_amount - jed.credit_amount), 0) as current_balance
      FROM ${AppConstants.accountsTable} a
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.is_active = 1 
        AND (a.type IN (?, ?, ?) OR a.type IN (?, ?))
        AND (je.is_posted = 1 OR je.id IS NULL)
        AND (je.entry_date <= ? OR je.id IS NULL)
      GROUP BY a.type, a.id, a.code, a.name, a.balance
      ORDER BY a.type, a.code
    ''';

    final result = await db.rawQuery(query, [
      AppConstants.accountTypeAsset,
      AppConstants.accountTypeLiability,
      AppConstants.accountTypeEquity,
      AppConstants.accountTypeRevenue,
      AppConstants.accountTypeExpense,
      asOfDate.toIso8601String().split('T')[0],
    ]);

    // تجميع البيانات حسب النوع
    List<BalanceSheetItem> assets = [];
    List<BalanceSheetItem> liabilities = [];
    List<BalanceSheetItem> equity = [];
    double totalAssets = 0;
    double totalLiabilities = 0;
    double totalEquity = 0;

    for (final row in result) {
      final type = row['type'] as String;
      final balance = (row['current_balance'] as num).toDouble();

      final item = BalanceSheetItem(
        accountId: row['id'] as int,
        accountCode: row['code'] as String,
        accountName: row['name'] as String,
        balance: balance,
      );

      switch (type) {
        case AppConstants.accountTypeAsset:
          assets.add(item);
          totalAssets += balance;
          break;
        case AppConstants.accountTypeLiability:
          liabilities.add(item);
          totalLiabilities += balance;
          break;
        case AppConstants.accountTypeEquity:
          equity.add(item);
          totalEquity += balance;
          break;
      }
    }

    final balanceSheetData = BalanceSheetReportData(
      asOfDate: asOfDate,
      assets: assets,
      liabilities: liabilities,
      equity: equity,
      totalAssets: totalAssets,
      totalLiabilities: totalLiabilities,
      totalEquity: totalEquity,
    );

    return InteractiveReportResult(
      reportType: 'balance_sheet',
      title: 'الميزانية العمومية التفاعلية',
      data: balanceSheetData,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: assets.length + liabilities.length + equity.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
      ),
    );
  }

  /// بناء جملة ORDER BY
  String _buildOrderByClause(String? sortBy, String? sortDirection) {
    final direction = sortDirection?.toUpperCase() == 'DESC' ? 'DESC' : 'ASC';

    switch (sortBy) {
      case 'code':
        return 'a.code $direction';
      case 'name':
        return 'a.name $direction';
      case 'type':
        return 'a.type $direction, a.code ASC';
      case 'balance':
        return 'a.balance $direction';
      default:
        return 'a.code ASC';
    }
  }

  /// تقرير حركة الحسابات التفاعلي
  Future<InteractiveReportResult> _getInteractiveAccountMovements(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    // يجب أن يكون هناك accountId في الفلاتر
    if (filters.accountIds.isEmpty) {
      throw Exception('يجب تحديد حساب لعرض حركته');
    }

    final accountId = filters.accountIds.first;
    String whereClause = 'WHERE jed.account_id = ?';
    List<dynamic> params = [accountId];

    if (filters.fromDate != null && filters.toDate != null) {
      whereClause += ' AND je.entry_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT
        je.entry_date,
        je.entry_number,
        je.description as entry_description,
        jed.debit_amount,
        jed.credit_amount,
        jed.description as detail_description
      FROM ${AppConstants.journalEntryDetailsTable} jed
      JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      $whereClause AND je.is_posted = 1
      ORDER BY je.entry_date ASC, je.id ASC
    ''';

    final result = await db.rawQuery(query, params);
    final data = result.map((row) => AccountMovementItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'account_movements',
      title: 'كشف حساب تفاعلي',
      data: data,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
      ),
    );
  }

  /// تقرير أعمار العملاء التفاعلي
  Future<InteractiveReportResult> _getInteractiveCustomerAging(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    final query =
        '''
      SELECT
        c.id,
        c.code,
        c.name,
        c.balance,
        CASE
          WHEN julianday('now') - julianday(MAX(je.entry_date)) <= 30 THEN c.balance
          ELSE 0
        END as current_amount,
        CASE
          WHEN julianday('now') - julianday(MAX(je.entry_date)) BETWEEN 31 AND 60 THEN c.balance
          ELSE 0
        END as days_31_60,
        CASE
          WHEN julianday('now') - julianday(MAX(je.entry_date)) BETWEEN 61 AND 90 THEN c.balance
          ELSE 0
        END as days_61_90,
        CASE
          WHEN julianday('now') - julianday(MAX(je.entry_date)) > 90 THEN c.balance
          ELSE 0
        END as over_90_days
      FROM ${AppConstants.customersTable} c
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON c.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE c.is_active = 1 AND c.balance > 0
      GROUP BY c.id, c.code, c.name, c.balance
      ORDER BY c.balance DESC
    ''';

    final result = await db.rawQuery(query);
    final data = result.map((row) => CustomerAgingItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'customer_aging',
      title: 'تقرير أعمار العملاء',
      data: data,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
      ),
    );
  }

  /// تقرير المخزون التفاعلي
  Future<InteractiveReportResult> _getInteractiveInventoryReport(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = 'WHERE i.is_active = 1';
    List<dynamic> params = [];

    if (filters.itemIds.isNotEmpty) {
      whereClause +=
          ' AND i.id IN (${filters.itemIds.map((_) => '?').join(',')})';
      params.addAll(filters.itemIds);
    }

    final query =
        '''
      SELECT
        i.id,
        i.code,
        i.name,
        i.unit,
        i.quantity,
        i.min_quantity,
        i.cost_price,
        i.selling_price,
        (i.quantity * i.cost_price) as total_cost_value,
        (i.quantity * i.selling_price) as total_selling_value,
        CASE
          WHEN i.quantity <= i.min_quantity THEN 'low_stock'
          WHEN i.quantity = 0 THEN 'out_of_stock'
          ELSE 'in_stock'
        END as stock_status
      FROM ${AppConstants.itemsTable} i
      $whereClause
      ORDER BY
        CASE
          WHEN i.quantity = 0 THEN 1
          WHEN i.quantity <= i.min_quantity THEN 2
          ELSE 3
        END,
        i.name ASC
    ''';

    final result = await db.rawQuery(query, params);
    final data = result.map((row) => InventoryReportItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'inventory_report',
      title: 'تقرير المخزون التفاعلي',
      data: data,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
      ),
    );
  }

  /// تقرير تحليل المبيعات التفاعلي
  Future<InteractiveReportResult> _getInteractiveSalesAnalysis(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = "WHERE i.type = 'sale'";
    List<dynamic> params = [];

    if (filters.fromDate != null && filters.toDate != null) {
      whereClause += ' AND i.invoice_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT
        DATE(i.invoice_date) as sale_date,
        COUNT(i.id) as invoice_count,
        SUM(i.total_amount) as total_sales,
        AVG(i.total_amount) as average_invoice,
        SUM(i.tax_amount) as total_tax
      FROM ${AppConstants.invoicesTable} i
      $whereClause
      GROUP BY DATE(i.invoice_date)
      ORDER BY sale_date DESC
    ''';

    final result = await db.rawQuery(query, params);
    final data = result.map((row) => SalesAnalysisItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'sales_analysis',
      title: 'تحليل المبيعات التفاعلي',
      data: data,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
      ),
    );
  }

  /// تقرير تحليل المشتريات التفاعلي
  Future<InteractiveReportResult> _getInteractivePurchaseAnalysis(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = "WHERE i.type = 'purchase'";
    List<dynamic> params = [];

    if (filters.fromDate != null && filters.toDate != null) {
      whereClause += ' AND i.invoice_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT
        DATE(i.invoice_date) as purchase_date,
        COUNT(i.id) as invoice_count,
        SUM(i.total_amount) as total_purchases,
        AVG(i.total_amount) as average_invoice,
        SUM(i.tax_amount) as total_tax
      FROM ${AppConstants.invoicesTable} i
      $whereClause
      GROUP BY DATE(i.invoice_date)
      ORDER BY purchase_date DESC
    ''';

    final result = await db.rawQuery(query, params);
    final data = result
        .map((row) => PurchaseAnalysisItem.fromMap(row))
        .toList();

    return InteractiveReportResult(
      reportType: 'purchase_analysis',
      title: 'تحليل المشتريات التفاعلي',
      data: data,
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
      ),
    );
  }

  /// تقرير حالة التكامل
  Future<InteractiveReportResult> _getIntegrationStatusReport(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    try {
      final integrationData = await _enhancedInvoiceService
          .validateAllInvoicesIntegration();

      return InteractiveReportResult(
        reportType: 'integration_status',
        title: 'تقرير حالة التكامل',
        data: integrationData,
        filters: filters,
        configuration: config ?? ReportConfiguration.defaultConfig(),
        metadata: ReportMetadata(
          totalRecords: 1,
          generatedAt: DateTime.now(),
          executionTimeMs: 0,
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير حالة التكامل',
        category: 'InteractiveReports',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تقرير المخزون المتقدم
  Future<InteractiveReportResult> _getAdvancedInventoryReport(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = 'WHERE i.is_active = 1';
      List<dynamic> params = [];

      if (filters.searchText != null && filters.searchText!.isNotEmpty) {
        whereClause += ' AND (i.name LIKE ? OR i.code LIKE ?)';
        params.addAll(['%${filters.searchText}%', '%${filters.searchText}%']);
      }

      final query =
          '''
        SELECT
          i.id,
          i.code,
          i.name,
          i.unit,
          i.quantity,
          i.min_quantity,
          i.cost_price,
          i.selling_price,
          (i.quantity * i.cost_price) as total_cost_value,
          (i.quantity * i.selling_price) as total_selling_value,
          CASE
            WHEN i.quantity <= 0 THEN 'نفد المخزون'
            WHEN i.quantity <= i.min_quantity THEN 'مخزون منخفض'
            ELSE 'متوفر'
          END as stock_status,
          COALESCE(il.quantity, 0) as warehouse_quantity,
          COALESCE(il.quantity, 0) - COALESCE(il.reserved_quantity, 0) as available_quantity,
          COALESCE(il.reserved_quantity, 0) as reserved_quantity,
          w.name as warehouse_name,
          l.name as location_name
        FROM items i
        LEFT JOIN item_locations il ON i.id = il.item_id
        LEFT JOIN warehouses w ON il.warehouse_id = w.id
        LEFT JOIN locations l ON il.location_id = l.id
        $whereClause
        ORDER BY i.name ASC, w.name ASC, l.name ASC
      ''';

      final result = await db.rawQuery(query, params);
      final data = result
          .map((row) => InventoryReportItem.fromMap(row))
          .toList();

      return InteractiveReportResult(
        reportType: 'advanced_inventory',
        title: 'تقرير المخزون المتقدم',
        data: data,
        filters: filters,
        configuration: config ?? ReportConfiguration.defaultConfig(),
        metadata: ReportMetadata(
          totalRecords: data.length,
          generatedAt: DateTime.now(),
          executionTimeMs: 0,
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير المخزون المتقدم',
        category: 'InteractiveReports',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تقرير تحليل الأداء
  Future<InteractiveReportResult> _getPerformanceAnalysisReport(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    try {
      // إحصائيات قاعدة البيانات
      final dbStats = await _getDatabaseStatistics();

      // إحصائيات العمليات
      final operationStats = await _getOperationStatistics(filters);

      final performanceData = {
        'databaseStats': dbStats,
        'operationStats': operationStats,
        'generatedAt': DateTime.now().toIso8601String(),
      };

      return InteractiveReportResult(
        reportType: 'performance_analysis',
        title: 'تحليل الأداء',
        data: performanceData,
        filters: filters,
        configuration: config ?? ReportConfiguration.defaultConfig(),
        metadata: ReportMetadata(
          totalRecords: 1,
          generatedAt: DateTime.now(),
          executionTimeMs: 0,
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير تحليل الأداء',
        category: 'InteractiveReports',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تقرير سجل المراجعة
  Future<InteractiveReportResult> _getAuditLogReport(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    try {
      final auditLogs = await AuditService.getAuditLogs(
        startDate: filters.fromDate,
        endDate: filters.toDate,
        entityType: filters.customFilters['entityType'],
        action: filters.customFilters['action'],
        limit: config?.pageSize ?? 100,
      );

      return InteractiveReportResult(
        reportType: 'audit_log',
        title: 'سجل المراجعة',
        data: auditLogs,
        filters: filters,
        configuration: config ?? ReportConfiguration.defaultConfig(),
        metadata: ReportMetadata(
          totalRecords: auditLogs.length,
          generatedAt: DateTime.now(),
          executionTimeMs: 0,
        ),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير سجل المراجعة',
        category: 'InteractiveReports',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات قاعدة البيانات
  Future<Map<String, dynamic>> _getDatabaseStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final tables = [
        'accounts',
        'invoices',
        'items',
        'customers',
        'suppliers',
        'journal_entries',
      ];
      final stats = <String, dynamic>{};

      for (final table in tables) {
        try {
          final result = await db.rawQuery(
            'SELECT COUNT(*) as count FROM $table',
          );
          stats['${table}_count'] = result.first['count'] ?? 0;
        } catch (e) {
          stats['${table}_count'] = 0;
        }
      }

      return stats;
    } catch (e) {
      return {};
    }
  }

  /// الحصول على إحصائيات العمليات
  Future<Map<String, dynamic>> _getOperationStatistics(
    ReportFilters filters,
  ) async {
    try {
      final db = await _databaseHelper.database;

      String dateFilter = '';
      List<dynamic> params = [];

      if (filters.fromDate != null) {
        dateFilter += ' AND created_at >= ?';
        params.add(filters.fromDate!.toIso8601String());
      }

      if (filters.toDate != null) {
        dateFilter += ' AND created_at <= ?';
        params.add(filters.toDate!.toIso8601String());
      }

      final query =
          '''
        SELECT
          COUNT(*) as total_operations,
          COUNT(CASE WHEN operation = 'CREATE' THEN 1 END) as create_operations,
          COUNT(CASE WHEN operation = 'UPDATE' THEN 1 END) as update_operations,
          COUNT(CASE WHEN operation = 'DELETE' THEN 1 END) as delete_operations
        FROM audit_log
        WHERE 1=1 $dateFilter
      ''';

      final result = await db.rawQuery(query, params);
      return result.isNotEmpty ? result.first : {};
    } catch (e) {
      return {};
    }
  }

  /// تقرير أعمار الموردين التفاعلي
  Future<InteractiveReportResult> _getInteractiveSupplierAging(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    final query = '''
      SELECT
        s.id,
        s.code,
        s.name,
        COALESCE(SUM(CASE
          WHEN julianday('now') - julianday(i.invoice_date) <= 30 THEN i.total_amount - COALESCE(p.paid_amount, 0)
          ELSE 0
        END), 0) as current_balance,
        COALESCE(SUM(CASE
          WHEN julianday('now') - julianday(i.invoice_date) BETWEEN 31 AND 60 THEN i.total_amount - COALESCE(p.paid_amount, 0)
          ELSE 0
        END), 0) as days_31_60,
        COALESCE(SUM(CASE
          WHEN julianday('now') - julianday(i.invoice_date) BETWEEN 61 AND 90 THEN i.total_amount - COALESCE(p.paid_amount, 0)
          ELSE 0
        END), 0) as days_61_90,
        COALESCE(SUM(CASE
          WHEN julianday('now') - julianday(i.invoice_date) > 90 THEN i.total_amount - COALESCE(p.paid_amount, 0)
          ELSE 0
        END), 0) as over_90_days
      FROM suppliers s
      LEFT JOIN invoices i ON s.id = i.supplier_id AND i.invoice_type = 'purchase'
      LEFT JOIN (
        SELECT invoice_id, SUM(amount) as paid_amount
        FROM payments
        GROUP BY invoice_id
      ) p ON i.id = p.invoice_id
      WHERE s.is_active = 1
      GROUP BY s.id, s.code, s.name
      HAVING (current_balance + days_31_60 + days_61_90 + over_90_days) > 0
      ORDER BY s.name
    ''';

    final result = await db.rawQuery(query);
    final data = result.map((row) => SupplierAgingItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'supplier_aging',
      title: 'تقرير أعمار الموردين',
      data: data,
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
        additionalInfo: {
          'total_balance': data.fold<double>(
            0,
            (sum, item) => sum + item.totalBalance,
          ),
        },
      ),
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
    );
  }

  /// تقرير اليومية العامة التفاعلي
  Future<InteractiveReportResult> _getInteractiveGeneralJournal(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = 'WHERE 1=1';
    List<dynamic> params = [];

    if (filters.fromDate != null && filters.toDate != null) {
      whereClause += ' AND je.entry_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT
        je.id,
        je.entry_number,
        je.entry_date,
        je.description,
        je.reference,
        jed.account_id,
        a.code as account_code,
        a.name as account_name,
        jed.debit_amount,
        jed.credit_amount,
        jed.description as line_description
      FROM journal_entries je
      JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
      JOIN accounts a ON jed.account_id = a.id
      $whereClause
      ORDER BY je.entry_date DESC, je.entry_number DESC, jed.id
    ''';

    final result = await db.rawQuery(query, params);
    final data = result.map((row) => GeneralJournalItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'general_journal',
      title: 'اليومية العامة',
      data: data,
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
        additionalInfo: {
          'total_debit': data.fold<double>(
            0,
            (sum, item) => sum + item.debitAmount,
          ),
          'total_credit': data.fold<double>(
            0,
            (sum, item) => sum + item.creditAmount,
          ),
        },
      ),
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
    );
  }

  /// تقرير الأستاذ العام التفاعلي
  Future<InteractiveReportResult> _getInteractiveGeneralLedger(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = 'WHERE a.is_active = 1';
    List<dynamic> params = [];

    if (filters.accountCodes.isNotEmpty) {
      whereClause +=
          ' AND a.code IN (${filters.accountCodes.map((_) => '?').join(',')})';
      params.addAll(filters.accountCodes);
    }

    if (filters.fromDate != null && filters.toDate != null) {
      whereClause += ' AND je.entry_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT
        a.id,
        a.code,
        a.name,
        a.type,
        COALESCE(SUM(jed.debit_amount), 0) as total_debit,
        COALESCE(SUM(jed.credit_amount), 0) as total_credit,
        COALESCE(a.opening_balance, 0) as opening_balance
      FROM accounts a
      LEFT JOIN journal_entry_details jed ON a.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
      $whereClause
      GROUP BY a.id, a.code, a.name, a.type, a.opening_balance
      ORDER BY a.code
    ''';

    final result = await db.rawQuery(query, params);
    final data = result.map((row) => GeneralLedgerItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'general_ledger',
      title: 'الأستاذ العام',
      data: data,
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
        additionalInfo: {
          'total_debit': data.fold<double>(
            0,
            (sum, item) => sum + item.totalDebit,
          ),
          'total_credit': data.fold<double>(
            0,
            (sum, item) => sum + item.totalCredit,
          ),
        },
      ),
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
    );
  }

  /// تقرير الضرائب التفاعلي
  Future<InteractiveReportResult> _getInteractiveTaxReport(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = 'WHERE 1=1';
    List<dynamic> params = [];

    if (filters.fromDate != null && filters.toDate != null) {
      whereClause += ' AND i.invoice_date BETWEEN ? AND ?';
      params.addAll([
        filters.fromDate!.toIso8601String().split('T')[0],
        filters.toDate!.toIso8601String().split('T')[0],
      ]);
    }

    final query =
        '''
      SELECT
        i.invoice_type,
        COUNT(*) as invoice_count,
        SUM(i.subtotal) as subtotal,
        SUM(i.tax_amount) as tax_amount,
        SUM(i.total_amount) as total_amount
      FROM invoices i
      $whereClause
      GROUP BY i.invoice_type
      ORDER BY i.invoice_type
    ''';

    final result = await db.rawQuery(query, params);
    final data = result.map((row) => TaxReportItem.fromMap(row)).toList();

    return InteractiveReportResult(
      reportType: 'tax_report',
      title: 'تقرير الضرائب',
      data: data,
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
        additionalInfo: {
          'total_tax': data.fold<double>(
            0,
            (sum, item) => sum + item.taxAmount,
          ),
          'total_subtotal': data.fold<double>(
            0,
            (sum, item) => sum + item.subtotal,
          ),
        },
      ),
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
    );
  }

  /// التقارير المخصصة التفاعلية
  Future<InteractiveReportResult> _getInteractiveCustomReports(
    ReportFilters filters,
    ReportConfiguration? config,
  ) async {
    // تقرير مخصص يجمع إحصائيات متنوعة
    final db = await _databaseHelper.database;

    final accountsCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM accounts WHERE is_active = 1',
    );
    final invoicesCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM invoices',
    );
    final customersCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM customers WHERE is_active = 1',
    );
    final suppliersCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM suppliers WHERE is_active = 1',
    );

    final data = [
      CustomReportItem(
        category: 'الحسابات',
        name: 'إجمالي الحسابات النشطة',
        value: (accountsCount.first['count'] as int).toDouble(),
        description: 'عدد الحسابات المفعلة في النظام',
      ),
      CustomReportItem(
        category: 'الفواتير',
        name: 'إجمالي الفواتير',
        value: (invoicesCount.first['count'] as int).toDouble(),
        description: 'عدد الفواتير الإجمالي',
      ),
      CustomReportItem(
        category: 'العملاء',
        name: 'إجمالي العملاء النشطين',
        value: (customersCount.first['count'] as int).toDouble(),
        description: 'عدد العملاء المفعلين',
      ),
      CustomReportItem(
        category: 'الموردين',
        name: 'إجمالي الموردين النشطين',
        value: (suppliersCount.first['count'] as int).toDouble(),
        description: 'عدد الموردين المفعلين',
      ),
    ];

    return InteractiveReportResult(
      reportType: 'custom_reports',
      title: 'التقارير المخصصة',
      data: data,
      metadata: ReportMetadata(
        totalRecords: data.length,
        generatedAt: DateTime.now(),
        executionTimeMs: 0,
        additionalInfo: {
          'report_type': 'system_overview',
          'categories': data.map((e) => e.category).toSet().length,
        },
      ),
      filters: filters,
      configuration: config ?? ReportConfiguration.defaultConfig(),
    );
  }

  /// تطبيق فلاتر إضافية على البيانات
  List<T> _applyAdditionalFilters<T>(List<T> data, ReportFilters filters) {
    // يمكن إضافة فلاتر إضافية هنا حسب الحاجة
    return data;
  }
}
