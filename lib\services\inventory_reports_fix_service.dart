/// خدمة إصلاح تقارير المخزون
/// تحل مشكلة العمود المفقود available_quantity
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';

class InventoryReportsFixService {
  static final InventoryReportsFixService _instance = InventoryReportsFixService._internal();
  factory InventoryReportsFixService() => _instance;
  InventoryReportsFixService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// تقرير المخزون مع حساب available_quantity
  Future<List<Map<String, dynamic>>> getInventoryReportSafe({
    int? warehouseId,
    bool lowStockOnly = false,
  }) async {
    try {
      final db = await _databaseHelper.database;
      
      String whereClause = 'WHERE i.is_active = 1';
      List<dynamic> params = [];

      if (warehouseId != null) {
        whereClause += ' AND il.warehouse_id = ?';
        params.add(warehouseId);
      }

      if (lowStockOnly) {
        whereClause += ' AND COALESCE(il.quantity, 0) <= COALESCE(il.reorder_point, i.min_quantity, 0)';
      }

      final query = '''
        SELECT 
          i.id,
          i.code,
          i.name,
          i.unit,
          w.name as warehouse_name,
          COALESCE(il.quantity, 0.0) as current_quantity,
          COALESCE(il.reserved_quantity, 0.0) as reserved_quantity,
          (COALESCE(il.quantity, 0.0) - COALESCE(il.reserved_quantity, 0.0)) as available_quantity,
          COALESCE(il.min_stock_level, i.min_quantity, 0.0) as min_stock_level,
          COALESCE(il.reorder_point, 0.0) as reorder_point,
          i.cost_price,
          i.selling_price,
          (COALESCE(il.quantity, 0.0) * COALESCE(i.cost_price, 0.0)) as total_value,
          CASE
            WHEN COALESCE(il.quantity, 0) <= 0 THEN 'نفد المخزون'
            WHEN COALESCE(il.quantity, 0) <= COALESCE(il.min_stock_level, i.min_quantity, 0) THEN 'مخزون منخفض'
            ELSE 'متوفر'
          END as stock_status
        FROM ${AppConstants.itemsTable} i
        LEFT JOIN ${AppConstants.itemLocationsTable} il ON i.id = il.item_id
        LEFT JOIN ${AppConstants.warehousesTable} w ON il.warehouse_id = w.id
        $whereClause
        ORDER BY i.code, w.name
      ''';

      final result = await db.rawQuery(query, params);
      
      LoggingService.info(
        'تم إنشاء تقرير المخزون بنجاح',
        category: 'InventoryReportsFixService',
        data: {'items_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير المخزون',
        category: 'InventoryReportsFixService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تقرير المخزون المنخفض
  Future<List<Map<String, dynamic>>> getLowStockReport() async {
    return await getInventoryReportSafe(lowStockOnly: true);
  }

  /// تقرير المخزون حسب المخزن
  Future<List<Map<String, dynamic>>> getInventoryByWarehouse(int warehouseId) async {
    return await getInventoryReportSafe(warehouseId: warehouseId);
  }

  /// إحصائيات المخزون
  Future<Map<String, dynamic>> getInventoryStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery('''
        SELECT
          COUNT(DISTINCT i.id) as total_items,
          COUNT(CASE WHEN COALESCE(il.quantity, 0) > 0 THEN 1 END) as items_in_stock,
          COUNT(CASE WHEN COALESCE(il.quantity, 0) = 0 THEN 1 END) as items_out_of_stock,
          COUNT(CASE WHEN COALESCE(il.reserved_quantity, 0) > 0 THEN 1 END) as items_with_reservations,
          SUM(COALESCE(il.quantity, 0)) as total_quantity,
          SUM(COALESCE(il.reserved_quantity, 0)) as total_reserved_quantity,
          SUM(COALESCE(il.quantity, 0) - COALESCE(il.reserved_quantity, 0)) as total_available_quantity,
          SUM(COALESCE(il.quantity, 0) * COALESCE(i.cost_price, 0)) as total_value
        FROM ${AppConstants.itemsTable} i
        LEFT JOIN ${AppConstants.itemLocationsTable} il ON i.id = il.item_id
        WHERE i.is_active = 1
      ''');

      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'total_items': row['total_items'] ?? 0,
          'items_in_stock': row['items_in_stock'] ?? 0,
          'items_out_of_stock': row['items_out_of_stock'] ?? 0,
          'items_with_reservations': row['items_with_reservations'] ?? 0,
          'total_quantity': (row['total_quantity'] as num?)?.toDouble() ?? 0.0,
          'total_reserved_quantity': (row['total_reserved_quantity'] as num?)?.toDouble() ?? 0.0,
          'total_available_quantity': (row['total_available_quantity'] as num?)?.toDouble() ?? 0.0,
          'total_value': (row['total_value'] as num?)?.toDouble() ?? 0.0,
        };
      }

      return {};
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات المخزون',
        category: 'InventoryReportsFixService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// فحص وإصلاح بيانات المخزون
  Future<bool> validateAndFixInventoryData() async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود بيانات غير صحيحة
      final invalidData = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM ${AppConstants.itemLocationsTable}
        WHERE quantity < 0 OR reserved_quantity < 0
      ''');

      final invalidCount = invalidData.first['count'] as int;
      
      if (invalidCount > 0) {
        LoggingService.warning(
          'تم العثور على بيانات مخزون غير صحيحة',
          category: 'InventoryReportsFixService',
          data: {'invalid_records': invalidCount},
        );

        // إصلاح البيانات السالبة
        await db.execute('''
          UPDATE ${AppConstants.itemLocationsTable}
          SET quantity = 0
          WHERE quantity < 0
        ''');

        await db.execute('''
          UPDATE ${AppConstants.itemLocationsTable}
          SET reserved_quantity = 0
          WHERE reserved_quantity < 0
        ''');

        LoggingService.info(
          'تم إصلاح بيانات المخزون غير الصحيحة',
          category: 'InventoryReportsFixService',
        );
      }

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص وإصلاح بيانات المخزون',
        category: 'InventoryReportsFixService',
        data: {'error': e.toString()},
      );
      return false;
    }
  }
}
