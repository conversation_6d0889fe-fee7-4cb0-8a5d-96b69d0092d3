import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../models/report_models.dart';
import '../models/interactive_report_models.dart';
import 'report_cache_service.dart';
import 'database_optimization_service.dart';
import 'progressive_loading_service.dart';
import 'report_performance_service.dart';
import 'logging_service.dart';

/// خدمة التقارير المحسنة
/// تدمج جميع تحسينات الأداء: التخزين المؤقت، التحميل التدريجي، مراقبة الأداء
class OptimizedReportsService {
  static final OptimizedReportsService _instance =
      OptimizedReportsService._internal();
  factory OptimizedReportsService() => _instance;
  OptimizedReportsService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ReportCacheService _cacheService = ReportCacheService();
  final DatabaseOptimizationService _dbOptimization =
      DatabaseOptimizationService();
  final ProgressiveLoadingService _progressiveLoading =
      ProgressiveLoadingService();
  final ReportPerformanceService _performanceService =
      ReportPerformanceService();

  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.info(
        'بدء تهيئة خدمة التقارير المحسنة',
        category: 'OptimizedReports',
      );

      // إنشاء الفهارس المحسنة
      await _dbOptimization.createOptimizedIndexes();

      // تحديث إحصائيات قاعدة البيانات
      await _dbOptimization.updateStatistics();

      _isInitialized = true;

      LoggingService.info(
        'تم تهيئة خدمة التقارير المحسنة بنجاح',
        category: 'OptimizedReports',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة خدمة التقارير المحسنة',
        category: 'OptimizedReports',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على ميزان المراجعة مع تحسينات الأداء
  Future<List<TrialBalanceItem>> getOptimizedTrialBalance({
    DateTime? fromDate,
    DateTime? toDate,
    bool useCache = true,
    bool useProgressiveLoading = false,
  }) async {
    await _ensureInitialized();

    final filters = {
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
    };

    final executionId = _performanceService.startReportExecution(
      reportType: 'trial_balance',
      filters: filters,
    );

    try {
      // محاولة الحصول من التخزين المؤقت
      if (useCache) {
        final cached = await _cacheService.getCachedReport(
          reportType: 'trial_balance',
          filters: filters,
        );

        if (cached != null) {
          _performanceService.endReportExecution(
            executionId: executionId,
            reportType: 'trial_balance',
            success: true,
            recordCount: (cached.data as List).length,
            additionalMetrics: {'source': 'cache'},
          );
          return cached.data as List<TrialBalanceItem>;
        }
      }

      // التحميل التدريجي للبيانات الكبيرة
      if (useProgressiveLoading) {
        final results = <TrialBalanceItem>[];
        await for (final batch
            in _progressiveLoading.loadTrialBalanceProgressive(
              fromDate: fromDate,
              toDate: toDate,
            )) {
          results.addAll(batch.data);
          if (batch.isComplete) break;
        }

        // حفظ في التخزين المؤقت
        if (useCache) {
          await _cacheService.cacheReport(
            reportType: 'trial_balance',
            filters: filters,
            reportData: results,
          );
        }

        _performanceService.endReportExecution(
          executionId: executionId,
          reportType: 'trial_balance',
          success: true,
          recordCount: results.length,
          additionalMetrics: {'source': 'progressive'},
        );

        return results;
      }

      // التحميل العادي المحسن
      final data = await _getTrialBalanceOptimized(fromDate, toDate);

      // حفظ في التخزين المؤقت
      if (useCache) {
        await _cacheService.cacheReport(
          reportType: 'trial_balance',
          filters: filters,
          reportData: data,
        );
      }

      _performanceService.endReportExecution(
        executionId: executionId,
        reportType: 'trial_balance',
        success: true,
        recordCount: data.length,
        additionalMetrics: {'source': 'database'},
      );

      return data;
    } catch (e) {
      _performanceService.endReportExecution(
        executionId: executionId,
        reportType: 'trial_balance',
        success: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// الحصول على قائمة الدخل مع تحسينات الأداء
  Future<ProfitLossReportData> getOptimizedProfitLoss({
    DateTime? fromDate,
    DateTime? toDate,
    bool useCache = true,
  }) async {
    await _ensureInitialized();

    final filters = {
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
    };

    final executionId = _performanceService.startReportExecution(
      reportType: 'profit_loss',
      filters: filters,
    );

    try {
      // محاولة الحصول من التخزين المؤقت
      if (useCache) {
        final cached = await _cacheService.getCachedReport(
          reportType: 'profit_loss',
          filters: filters,
        );

        if (cached != null) {
          _performanceService.endReportExecution(
            executionId: executionId,
            reportType: 'profit_loss',
            success: true,
            additionalMetrics: {'source': 'cache'},
          );
          return cached.data as ProfitLossReportData;
        }
      }

      final data = await _getProfitLossOptimized(fromDate, toDate);

      // حفظ في التخزين المؤقت
      if (useCache) {
        await _cacheService.cacheReport(
          reportType: 'profit_loss',
          filters: filters,
          reportData: data,
        );
      }

      _performanceService.endReportExecution(
        executionId: executionId,
        reportType: 'profit_loss',
        success: true,
        additionalMetrics: {'source': 'database'},
      );

      return data;
    } catch (e) {
      _performanceService.endReportExecution(
        executionId: executionId,
        reportType: 'profit_loss',
        success: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// الحصول على تقرير المخزون مع تحسينات الأداء
  Future<List<InventoryReportItem>> getOptimizedInventoryReport({
    String? category,
    String? searchTerm,
    bool useCache = true,
    bool useProgressiveLoading = false,
  }) async {
    await _ensureInitialized();

    final filters = {'category': category, 'searchTerm': searchTerm};

    final executionId = _performanceService.startReportExecution(
      reportType: 'inventory_report',
      filters: filters,
    );

    try {
      // محاولة الحصول من التخزين المؤقت
      if (useCache) {
        final cached = await _cacheService.getCachedReport(
          reportType: 'inventory_report',
          filters: filters,
        );

        if (cached != null) {
          _performanceService.endReportExecution(
            executionId: executionId,
            reportType: 'inventory_report',
            success: true,
            recordCount: (cached.data as List).length,
            additionalMetrics: {'source': 'cache'},
          );
          return cached.data as List<InventoryReportItem>;
        }
      }

      // التحميل التدريجي للبيانات الكبيرة
      if (useProgressiveLoading) {
        final results = <InventoryReportItem>[];
        await for (final batch in _progressiveLoading.loadInventoryProgressive(
          category: category,
          searchTerm: searchTerm,
        )) {
          results.addAll(batch.data);
          if (batch.isComplete) break;
        }

        // حفظ في التخزين المؤقت
        if (useCache) {
          await _cacheService.cacheReport(
            reportType: 'inventory_report',
            filters: filters,
            reportData: results,
          );
        }

        _performanceService.endReportExecution(
          executionId: executionId,
          reportType: 'inventory_report',
          success: true,
          recordCount: results.length,
          additionalMetrics: {'source': 'progressive'},
        );

        return results;
      }

      // التحميل العادي المحسن
      final data = await _getInventoryReportOptimized(category, searchTerm);

      // حفظ في التخزين المؤقت
      if (useCache) {
        await _cacheService.cacheReport(
          reportType: 'inventory_report',
          filters: filters,
          reportData: data,
        );
      }

      _performanceService.endReportExecution(
        executionId: executionId,
        reportType: 'inventory_report',
        success: true,
        recordCount: data.length,
        additionalMetrics: {'source': 'database'},
      );

      return data;
    } catch (e) {
      _performanceService.endReportExecution(
        executionId: executionId,
        reportType: 'inventory_report',
        success: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// إلغاء التخزين المؤقت عند تحديث البيانات
  Future<void> invalidateCache({
    List<String>? tableNames,
    String? reportType,
  }) async {
    if (tableNames != null) {
      await _cacheService.invalidateReportsForTables(tableNames);
    }

    if (reportType != null) {
      // سيتم تنفيذ إلغاء تخزين نوع تقرير محدد
    }

    LoggingService.info(
      'تم إلغاء التخزين المؤقت',
      category: 'OptimizedReports',
      data: {'tableNames': tableNames, 'reportType': reportType},
    );
  }

  /// الحصول على إحصائيات الأداء
  OptimizedReportsStatistics getPerformanceStatistics() {
    final cacheStats = _cacheService.getStatistics();
    final progressiveStats = _progressiveLoading.getStatistics();
    final performanceAnalysis = _performanceService.analyzeOverallPerformance();

    return OptimizedReportsStatistics(
      cacheStatistics: cacheStats,
      progressiveLoadingStatistics: progressiveStats,
      performanceAnalysis: performanceAnalysis,
    );
  }

  /// التأكد من التهيئة
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// ميزان المراجعة محسن
  Future<List<TrialBalanceItem>> _getTrialBalanceOptimized(
    DateTime? fromDate,
    DateTime? toDate,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery(
      '''
      SELECT
        a.id,
        a.code,
        a.name,
        a.type,
        COALESCE(SUM(jed.debit_amount), 0) as total_debit,
        COALESCE(SUM(jed.credit_amount), 0) as total_credit
      FROM ${AppConstants.accountsTable} a
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.is_active = 1
        ${fromDate != null ? "AND je.entry_date >= ?" : ''}
        ${toDate != null ? "AND je.entry_date <= ?" : ''}
        AND (je.is_posted = 1 OR je.is_posted IS NULL)
      GROUP BY a.id, a.code, a.name, a.type
      ORDER BY a.code
    ''',
      [
        if (fromDate != null) fromDate.toIso8601String().split('T')[0],
        if (toDate != null) toDate.toIso8601String().split('T')[0],
      ],
    );

    return result.map((row) => TrialBalanceItem.fromMap(row)).toList();
  }

  /// قائمة الدخل محسنة
  Future<ProfitLossReportData> _getProfitLossOptimized(
    DateTime? fromDate,
    DateTime? toDate,
  ) async {
    final db = await _databaseHelper.database;

    // الحصول على عناصر الإيرادات والمصروفات
    final itemsResult = await db.rawQuery(
      '''
      SELECT
        a.id as account_id,
        a.code as account_code,
        a.name as account_name,
        a.type as account_type,
        SUM(jed.debit_amount) as total_debit,
        SUM(jed.credit_amount) as total_credit
      FROM ${AppConstants.accountsTable} a
      JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.type IN ('revenue', 'expense')
        ${fromDate != null ? "AND je.entry_date >= ?" : ''}
        ${toDate != null ? "AND je.entry_date <= ?" : ''}
        AND je.is_posted = 1
      GROUP BY a.id, a.code, a.name, a.type
    ''',
      [
        if (fromDate != null) fromDate.toIso8601String().split('T')[0],
        if (toDate != null) toDate.toIso8601String().split('T')[0],
      ],
    );

    List<ProfitLossItem> revenueItems = [];
    List<ProfitLossItem> expenseItems = [];
    double totalRevenue = 0;
    double totalExpense = 0;

    for (final row in itemsResult) {
      final accountType = row['account_type'] as String;
      final credit = (row['total_credit'] as num?)?.toDouble() ?? 0;
      final debit = (row['total_debit'] as num?)?.toDouble() ?? 0;
      final amount = accountType == 'revenue'
          ? (credit - debit)
          : (debit - credit);

      final item = ProfitLossItem(
        accountId: row['account_id'] as int,
        accountCode: row['account_code'] as String,
        accountName: row['account_name'] as String,
        amount: amount,
      );

      if (accountType == 'revenue') {
        revenueItems.add(item);
        totalRevenue += amount;
      } else if (accountType == 'expense') {
        expenseItems.add(item);
        totalExpense += amount;
      }
    }

    // استخدام التواريخ الافتراضية إذا لم تكن محددة
    final effectiveFromDate = fromDate ?? DateTime(DateTime.now().year, 1, 1);
    final effectiveToDate = toDate ?? DateTime.now();

    return ProfitLossReportData(
      fromDate: effectiveFromDate,
      toDate: effectiveToDate,
      revenueItems: revenueItems,
      expenseItems: expenseItems,
      totalRevenue: totalRevenue,
      totalExpense: totalExpense,
      netProfit: totalRevenue - totalExpense,
    );
  }

  /// تقرير المخزون محسن
  Future<List<InventoryReportItem>> _getInventoryReportOptimized(
    String? category,
    String? searchTerm,
  ) async {
    final db = await _databaseHelper.database;

    String whereClause = 'WHERE i.is_active = 1';
    List<dynamic> params = [];

    if (category != null && category.isNotEmpty) {
      whereClause += ' AND i.category_id = ?';
      params.add(category);
    }

    if (searchTerm != null && searchTerm.isNotEmpty) {
      whereClause += ' AND (i.name LIKE ? OR i.code LIKE ?)';
      params.addAll(['%$searchTerm%', '%$searchTerm%']);
    }

    final result = await db.rawQuery('''
      SELECT 
        i.id,
        i.code,
        i.name,
        i.unit,
        i.quantity,
        i.min_quantity,
        i.cost_price,
        i.selling_price,
        (i.quantity * i.cost_price) as total_cost_value,
        (i.quantity * i.selling_price) as total_selling_value,
        CASE 
          WHEN i.quantity <= i.min_quantity THEN 'low_stock'
          WHEN i.quantity = 0 THEN 'out_of_stock'
          ELSE 'in_stock'
        END as stock_status
      FROM ${AppConstants.itemsTable} i
      $whereClause
      ORDER BY 
        CASE 
          WHEN i.quantity = 0 THEN 1
          WHEN i.quantity <= i.min_quantity THEN 2
          ELSE 3
        END,
        i.name ASC
    ''', params);

    return result.map((row) => InventoryReportItem.fromMap(row)).toList();
  }
}

/// إحصائيات الخدمة المحسنة
class OptimizedReportsStatistics {
  final ReportCacheStatistics cacheStatistics;
  final ProgressiveLoadingStatistics progressiveLoadingStatistics;
  final PerformanceAnalysis performanceAnalysis;

  const OptimizedReportsStatistics({
    required this.cacheStatistics,
    required this.progressiveLoadingStatistics,
    required this.performanceAnalysis,
  });

  Map<String, dynamic> toMap() {
    return {
      'cache': cacheStatistics.toMap(),
      'progressiveLoading': progressiveLoadingStatistics.toMap(),
      'performance': performanceAnalysis.toMap(),
    };
  }
}
