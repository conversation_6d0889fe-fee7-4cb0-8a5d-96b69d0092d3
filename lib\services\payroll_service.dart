/// خدمة إدارة الرواتب المتقدمة
/// توفر عمليات حساب وإدارة الرواتب مع التكامل المحاسبي
library;

import 'package:sqflite_sqlcipher/sqflite.dart';
import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

import '../services/attendance_service.dart';
import '../services/employee_service.dart';
import '../services/salary_components_service.dart';

import '../services/syrian_payroll_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class PayrollService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  final AttendanceService _attendanceService = AttendanceService();
  final EmployeeService _employeeService = EmployeeService();
  final SalaryComponentsService _salaryComponentsService =
      SalaryComponentsService();
  final SyrianPayrollService _syrianPayrollService = SyrianPayrollService();

  /// حساب راتب موظف لشهر معين
  Future<PayrollRecord> calculatePayroll({
    required int employeeId,
    required int month,
    required int year,
    double allowances = 0,
    double bonuses = 0,
    double otherDeductions = 0,
    String? notes,
  }) async {
    try {
      // التحقق من وجود جداول الموارد البشرية
      await _employeeService.ensureHRTablesExist();

      // الحصول على بيانات الموظف
      final employee = await _employeeService.getEmployeeById(employeeId);
      if (employee == null) {
        throw ValidationException('الموظف غير موجود');
      }

      // التحقق من عدم وجود كشف راتب لنفس الشهر
      final existingPayroll = await getPayrollRecord(employeeId, month, year);
      if (existingPayroll != null) {
        throw ValidationException('تم إنشاء كشف راتب لهذا الشهر مسبقاً');
      }

      // حساب أيام العمل في الشهر
      final workingDays = _calculateWorkingDaysInMonth(month, year);

      // الحصول على بيانات الحضور للشهر
      final fromDate = DateTime(year, month, 1);
      final toDate = DateTime(year, month + 1, 0);
      final attendanceRecords = await _attendanceService.getAttendanceByPeriod(
        employeeId: employeeId,
        fromDate: fromDate,
        toDate: toDate,
      );

      // حساب الأيام الفعلية وساعات العمل
      final actualWorkingDays = attendanceRecords
          .where((a) => a.status == 'present')
          .length;
      final overtimeHours = attendanceRecords.fold<double>(
        0,
        (sum, attendance) => sum + attendance.overtimeHours,
      );

      // حساب الراتب الأساسي المتناسب
      final proportionalBasicSalary =
          (employee.basicSalary * actualWorkingDays) / workingDays;

      // حساب أجر الساعات الإضافية
      final hourlyRate =
          employee.basicSalary / (workingDays * 8); // 8 ساعات يومياً
      final overtimePay =
          overtimeHours * hourlyRate * 1.5; // 1.5 ضعف الساعة العادية

      // حساب البدلات والحوافز من مكونات الراتب المخصصة
      final salaryComponents = await _calculateEmployeeSalaryComponents(
        employeeId,
        proportionalBasicSalary,
      );

      final calculatedAllowances = salaryComponents['allowances'] as double;
      final calculatedBonuses = salaryComponents['bonuses'] as double;
      final calculatedDeductions = salaryComponents['deductions'] as double;

      // حساب الراتب الإجمالي مع المكونات المخصصة
      final grossSalary =
          proportionalBasicSalary +
          calculatedAllowances +
          allowances + // البدلات الإضافية المدخلة يدوياً
          overtimePay +
          calculatedBonuses +
          bonuses; // الحوافز الإضافية المدخلة يدوياً

      // حساب الضرائب والتأمينات باستخدام النظام السوري المتقدم
      final salaryCalculation = await _syrianPayrollService
          .calculateEmployeeSalary(
            employeeId: employeeId,
            month: month,
            year: year,
            additionalAllowances: allowances,
            additionalDeductions: otherDeductions,
            bonusAmount: bonuses,
          );

      // استخدام النتائج من الحساب السوري المتقدم
      final taxAmount = salaryCalculation.taxAmount;
      final socialInsuranceAmount = salaryCalculation.insuranceAmount;

      // حساب خصم القروض (إن وجدت)
      final loanDeductions = await _calculateLoanDeductions(
        employeeId,
        month,
        year,
      );

      // حساب إجمالي الاستقطاعات مع المكونات المخصصة
      final totalDeductions =
          taxAmount +
          socialInsuranceAmount +
          calculatedDeductions + // الاستقطاعات من مكونات الراتب
          loanDeductions +
          otherDeductions;

      // حساب صافي الراتب
      final netSalary = grossSalary - totalDeductions;

      final payrollRecord = PayrollRecord(
        employeeId: employeeId,
        month: month,
        year: year,
        basicSalary: proportionalBasicSalary,
        allowances:
            calculatedAllowances + allowances, // البدلات المحسوبة + الإضافية
        bonuses: calculatedBonuses + bonuses, // الحوافز المحسوبة + الإضافية
        overtimeAmount: overtimePay,
        grossSalary: grossSalary,
        incomeTax: taxAmount,
        socialInsurance: socialInsuranceAmount,
        loanDeductions: loanDeductions,
        otherDeductions:
            calculatedDeductions +
            otherDeductions, // الاستقطاعات المحسوبة + الإضافية
        totalDeductions: totalDeductions,
        netSalary: netSalary,
        workingDays: workingDays,
        actualWorkingDays: actualWorkingDays,
        overtimeHours: overtimeHours,
        absenceDays: workingDays - actualWorkingDays,
        status: 'calculated',
        notes: notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ كشف الراتب
      final db = await _databaseHelper.database;
      final id = await db.insert('payroll_records', payrollRecord.toMap());

      final savedRecord = payrollRecord.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'PayrollRecord',
        entityId: id,
        description: 'حساب راتب الموظف للشهر $month/$year',
        newValues: savedRecord.toMap(),
      );

      LoggingService.info(
        'تم حساب راتب الموظف بنجاح',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'netSalary': netSalary,
        },
      );

      return savedRecord;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب الراتب',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// حساب أيام العمل في الشهر (باستثناء الجمعة والسبت)
  int _calculateWorkingDaysInMonth(int month, int year) {
    final lastDay = DateTime(year, month + 1, 0);

    int workingDays = 0;
    for (int day = 1; day <= lastDay.day; day++) {
      final date = DateTime(year, month, day);
      // استثناء الجمعة (5) والسبت (6)
      if (date.weekday != DateTime.friday &&
          date.weekday != DateTime.saturday) {
        workingDays++;
      }
    }

    return workingDays;
  }

  /// حساب خصم القروض للشهر
  Future<double> _calculateLoanDeductions(
    int employeeId,
    int month,
    int year,
  ) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على الأقساط المستحقة في الشهر المحدد
      final result = await db.rawQuery(
        '''
        SELECT COALESCE(SUM(li.amount), 0) as total_deduction
        FROM ${AppConstants.loanInstallmentsTable} li
        INNER JOIN ${AppConstants.loansTable} l ON li.loan_id = l.id
        WHERE l.employee_id = ?
        AND li.status = 'pending'
        AND strftime('%Y', li.due_date) = ?
        AND strftime('%m', li.due_date) = ?
      ''',
        [employeeId, year.toString(), month.toString().padLeft(2, '0')],
      );

      return (result.first['total_deduction'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب خصم القروض',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      return 0.0;
    }
  }

  /// الحصول على كشف راتب موظف لشهر معين
  Future<PayrollRecord?> getPayrollRecord(
    int employeeId,
    int month,
    int year,
  ) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود جدول الرواتب
      final tableExists = await _checkTableExists(db, 'payroll_records');
      if (!tableExists) {
        LoggingService.warning(
          'جدول الرواتب غير موجود',
          category: 'PayrollService',
        );
        return null;
      }

      final result = await db.query(
        'payroll_records',
        where: 'employee_id = ? AND month = ? AND year = ?',
        whereArgs: [employeeId, month, year],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return PayrollRecord.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على كشف الراتب',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// حساب مكونات الراتب المخصصة للموظف
  Future<Map<String, double>> _calculateEmployeeSalaryComponents(
    int employeeId,
    double basicSalary,
  ) async {
    try {
      double totalAllowances = 0;
      double totalBonuses = 0;
      double totalDeductions = 0;

      // الحصول على مكونات الراتب المخصصة للموظف من جدول salary_details
      final db = await _databaseHelper.database;
      final employeeSalaryDetails = await db.query(
        AppConstants.salaryDetailsTable,
        where:
            'employee_id = ? AND is_active = 1 AND (effective_to IS NULL OR effective_to > ?)',
        whereArgs: [employeeId, DateTime.now().toIso8601String()],
      );

      if (employeeSalaryDetails.isNotEmpty) {
        // استخدام مكونات الراتب المخصصة للموظف
        for (final detail in employeeSalaryDetails) {
          double amount = (detail['amount'] as num).toDouble();
          final isPercentage = (detail['is_percentage'] as int) == 1;
          final componentType = detail['component_type'] as String;

          // إذا كان المكون نسبة مئوية، احسب القيمة
          if (isPercentage) {
            final percentageOf = detail['percentage_of'] as String?;
            final baseAmount = percentageOf == 'gross_salary'
                ? basicSalary // سنحسب على الراتب الأساسي مؤقتاً
                : basicSalary;
            amount = (baseAmount * amount) / 100;
          }

          // تصنيف المكون حسب النوع
          switch (componentType) {
            case 'allowance':
              totalAllowances += amount;
              break;
            case 'bonus':
              totalBonuses += amount;
              break;
            case 'deduction':
              totalDeductions += amount;
              break;
          }
        }
      } else {
        // إذا لم تكن هناك مكونات مخصصة، استخدم مكونات الراتب العامة النشطة
        final activeComponents = await _salaryComponentsService
            .getAllSalaryComponents(activeOnly: true);

        for (final component in activeComponents) {
          double amount = component.defaultAmount;

          // إذا كان المكون نسبة مئوية، احسب القيمة
          if (component.isPercentage) {
            final baseAmount = component.percentageOf == 'gross_salary'
                ? basicSalary // سنحسب على الراتب الأساسي مؤقتاً
                : basicSalary;
            amount = (baseAmount * component.defaultAmount) / 100;
          }

          // تصنيف المكون حسب النوع
          switch (component.type) {
            case 'allowance':
              totalAllowances += amount;
              break;
            case 'bonus':
              totalBonuses += amount;
              break;
            case 'deduction':
              totalDeductions += amount;
              break;
          }
        }
      }

      return {
        'allowances': totalAllowances,
        'bonuses': totalBonuses,
        'deductions': totalDeductions,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب مكونات الراتب للموظف',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'basicSalary': basicSalary,
          'error': e.toString(),
        },
      );

      // في حالة الخطأ، إرجاع قيم صفر
      return {'allowances': 0.0, 'bonuses': 0.0, 'deductions': 0.0};
    }
  }

  /// تطبيق قالب راتب على موظف
  Future<void> applySalaryTemplateToEmployee({
    required int employeeId,
    required List<SalaryComponent> components,
    required double basicSalary,
    DateTime? effectiveDate,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final effective = effectiveDate ?? DateTime.now();

      // إنهاء المكونات الحالية
      await db.update(
        AppConstants.salaryDetailsTable,
        {
          'effective_to': effective.toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'employee_id = ? AND effective_to IS NULL',
        whereArgs: [employeeId],
      );

      // تطبيق المكونات الجديدة
      for (final component in components) {
        await db.insert(AppConstants.salaryDetailsTable, {
          'employee_id': employeeId,
          'component_type': component.type,
          'component_name': component.name,
          'amount': component.defaultAmount,
          'is_percentage': component.isPercentage ? 1 : 0,
          'percentage_of': component.percentageOf,
          'is_taxable': component.isTaxable ? 1 : 0,
          'is_active': component.isActive ? 1 : 0,
          'effective_from': effective.toIso8601String(),
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      // تحديث الراتب الأساسي للموظف
      await db.update(
        AppConstants.employeesTable,
        {
          'basic_salary': basicSalary,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [employeeId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'APPLY_SALARY_TEMPLATE',
        entityType: 'Employee',
        entityId: employeeId,
        description: 'تطبيق قالب راتب على موظف',
        newValues: {
          'employee_id': employeeId,
          'basic_salary': basicSalary,
          'components_count': components.length,
          'effective_date': effective.toIso8601String(),
        },
      );

      LoggingService.info(
        'تم تطبيق قالب الراتب على الموظف بنجاح',
        category: 'PayrollService',
        data: {
          'employee_id': employeeId,
          'basic_salary': basicSalary,
          'components_count': components.length,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تطبيق قالب الراتب',
        category: 'PayrollService',
        data: {'employee_id': employeeId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حساب راتب متقدم باستخدام النظام السوري والمكونات المخصصة
  Future<SalaryCalculation> calculateAdvancedSalary({
    required int employeeId,
    required int month,
    required int year,
    double additionalAllowances = 0,
    double additionalDeductions = 0,
    double bonusAmount = 0,
  }) async {
    try {
      // استخدام الخدمة السورية المتقدمة مع المكونات المخصصة
      return await _syrianPayrollService.calculateEmployeeSalary(
        employeeId: employeeId,
        month: month,
        year: year,
        additionalAllowances: additionalAllowances,
        additionalDeductions: additionalDeductions,
        bonusAmount: bonusAmount,
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب الراتب المتقدم',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// فحص وجود جدول في قاعدة البيانات
  Future<bool> _checkTableExists(Database db, String tableName) async {
    try {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName],
      );
      return result.isNotEmpty;
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص وجود الجدول',
        category: 'PayrollService',
        data: {'table': tableName, 'error': e.toString()},
      );
      return false;
    }
  }
}
