import 'dart:async';
import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../models/report_models.dart';
import 'logging_service.dart';

/// خدمة التحميل التدريجي
/// توفر تحميل البيانات الكبيرة على دفعات لتحسين الأداء
class ProgressiveLoadingService {
  static final ProgressiveLoadingService _instance =
      ProgressiveLoadingService._internal();
  factory ProgressiveLoadingService() => _instance;
  ProgressiveLoadingService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إعدادات التحميل التدريجي
  static const int defaultPageSize = 50;
  static const int largeDataPageSize = 100;
  static const int maxConcurrentLoads = 3;

  final Map<String, StreamController<ProgressiveLoadResult>> _activeLoads = {};

  /// تحميل تدريجي لميزان المراجعة
  Stream<ProgressiveLoadResult<TrialBalanceItem>> loadTrialBalanceProgressive({
    DateTime? fromDate,
    DateTime? toDate,
    int pageSize = defaultPageSize,
  }) async* {
    final loadId = 'trial_balance_${DateTime.now().millisecondsSinceEpoch}';

    try {
      LoggingService.info(
        'بدء التحميل التدريجي لميزان المراجعة',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'pageSize': pageSize},
      );

      final db = await _databaseHelper.database;

      // حساب العدد الإجمالي
      final countResult = await db.rawQuery('''
        SELECT COUNT(DISTINCT a.id) as total_count
        FROM ${AppConstants.accountsTable} a
        WHERE a.is_active = 1
      ''');

      final totalCount = countResult.first['total_count'] as int;
      final totalPages = (totalCount / pageSize).ceil();

      for (int page = 0; page < totalPages; page++) {
        final offset = page * pageSize;

        final result = await db.rawQuery('''
          SELECT
            a.id,
            a.code,
            a.name,
            a.type,
            COALESCE(SUM(
              CASE
                WHEN jed.debit_amount IS NOT NULL THEN jed.debit_amount
                ELSE 0
              END
            ), 0) as total_debit,
            COALESCE(SUM(
              CASE
                WHEN jed.credit_amount IS NOT NULL THEN jed.credit_amount
                ELSE 0
              END
            ), 0) as total_credit
          FROM ${AppConstants.accountsTable} a
          LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
          LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
          WHERE a.is_active = 1
            ${fromDate != null ? "AND je.entry_date >= '${fromDate.toIso8601String().split('T')[0]}'" : ''}
            ${toDate != null ? "AND je.entry_date <= '${toDate.toIso8601String().split('T')[0]}'" : ''}
            AND (je.is_posted = 1 OR je.is_posted IS NULL)
          GROUP BY a.id, a.code, a.name, a.account_type
          ORDER BY a.code
          LIMIT $pageSize OFFSET $offset
        ''');

        final items = result
            .map((row) => TrialBalanceItem.fromMap(row))
            .toList();

        yield ProgressiveLoadResult<TrialBalanceItem>(
          loadId: loadId,
          data: items,
          currentPage: page + 1,
          totalPages: totalPages,
          totalItems: totalCount,
          isComplete: page == totalPages - 1,
          loadedItems: (page + 1) * pageSize,
        );

        // تأخير قصير لتجنب حجب الواجهة
        if (page < totalPages - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي لميزان المراجعة',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'error': e.toString()},
      );

      yield ProgressiveLoadResult<TrialBalanceItem>(
        loadId: loadId,
        data: [],
        currentPage: 0,
        totalPages: 0,
        totalItems: 0,
        isComplete: true,
        loadedItems: 0,
        error: e.toString(),
      );
    }
  }

  /// تحميل تدريجي لحركة الحسابات
  Stream<ProgressiveLoadResult<AccountMovementItem>>
  loadAccountMovementsProgressive({
    required int accountId,
    required DateTime fromDate,
    required DateTime toDate,
    int pageSize = defaultPageSize,
  }) async* {
    final loadId =
        'account_movements_${accountId}_${DateTime.now().millisecondsSinceEpoch}';

    try {
      LoggingService.info(
        'بدء التحميل التدريجي لحركة الحسابات',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'accountId': accountId, 'pageSize': pageSize},
      );

      final db = await _databaseHelper.database;

      // حساب العدد الإجمالي
      final countResult = await db.rawQuery(
        '''
        SELECT COUNT(*) as total_count
        FROM ${AppConstants.journalEntryDetailsTable} jed
        JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
        WHERE jed.account_id = ? 
          AND je.is_posted = 1
          AND je.entry_date BETWEEN ? AND ?
      ''',
        [
          accountId,
          fromDate.toIso8601String().split('T')[0],
          toDate.toIso8601String().split('T')[0],
        ],
      );

      final totalCount = countResult.first['total_count'] as int;
      final totalPages = (totalCount / pageSize).ceil();

      for (int page = 0; page < totalPages; page++) {
        final offset = page * pageSize;

        final result = await db.rawQuery(
          '''
          SELECT 
            je.entry_date,
            je.entry_number,
            je.description as entry_description,
            jed.debit_amount,
            jed.credit_amount,
            jed.description as detail_description
          FROM ${AppConstants.journalEntryDetailsTable} jed
          JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
          WHERE jed.account_id = ? 
            AND je.is_posted = 1
            AND je.entry_date BETWEEN ? AND ?
          ORDER BY je.entry_date ASC, je.id ASC
          LIMIT $pageSize OFFSET $offset
        ''',
          [
            accountId,
            fromDate.toIso8601String().split('T')[0],
            toDate.toIso8601String().split('T')[0],
          ],
        );

        final items = result
            .map((row) => AccountMovementItem.fromMap(row))
            .toList();

        yield ProgressiveLoadResult<AccountMovementItem>(
          loadId: loadId,
          data: items,
          currentPage: page + 1,
          totalPages: totalPages,
          totalItems: totalCount,
          isComplete: page == totalPages - 1,
          loadedItems: (page + 1) * pageSize,
        );

        if (page < totalPages - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي لحركة الحسابات',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'error': e.toString()},
      );

      yield ProgressiveLoadResult<AccountMovementItem>(
        loadId: loadId,
        data: [],
        currentPage: 0,
        totalPages: 0,
        totalItems: 0,
        isComplete: true,
        loadedItems: 0,
        error: e.toString(),
      );
    }
  }

  /// تحميل تدريجي للمخزون
  Stream<ProgressiveLoadResult<InventoryReportItem>> loadInventoryProgressive({
    String? category,
    String? searchTerm,
    int pageSize = defaultPageSize,
  }) async* {
    final loadId = 'inventory_${DateTime.now().millisecondsSinceEpoch}';

    try {
      LoggingService.info(
        'بدء التحميل التدريجي للمخزون',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'pageSize': pageSize},
      );

      final db = await _databaseHelper.database;

      // بناء شروط البحث
      String whereClause = 'WHERE i.is_active = 1';
      List<dynamic> params = [];

      if (category != null && category.isNotEmpty) {
        whereClause += ' AND i.category_id = ?';
        params.add(category);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        whereClause += ' AND (i.name LIKE ? OR i.code LIKE ?)';
        params.addAll(['%$searchTerm%', '%$searchTerm%']);
      }

      // حساب العدد الإجمالي
      final countResult = await db.rawQuery('''
        SELECT COUNT(*) as total_count
        FROM ${AppConstants.itemsTable} i
        $whereClause
      ''', params);

      final totalCount = countResult.first['total_count'] as int;
      final totalPages = (totalCount / pageSize).ceil();

      for (int page = 0; page < totalPages; page++) {
        final offset = page * pageSize;

        final result = await db.rawQuery('''
          SELECT 
            i.id,
            i.code,
            i.name,
            i.unit,
            i.quantity,
            i.min_quantity,
            i.cost_price,
            i.selling_price,
            (i.quantity * i.cost_price) as total_cost_value,
            (i.quantity * i.selling_price) as total_selling_value,
            CASE 
              WHEN i.quantity <= i.min_quantity THEN 'low_stock'
              WHEN i.quantity = 0 THEN 'out_of_stock'
              ELSE 'in_stock'
            END as stock_status
          FROM ${AppConstants.itemsTable} i
          $whereClause
          ORDER BY 
            CASE 
              WHEN i.quantity = 0 THEN 1
              WHEN i.quantity <= i.min_quantity THEN 2
              ELSE 3
            END,
            i.name ASC
          LIMIT $pageSize OFFSET $offset
        ''', params);

        final items = result
            .map((row) => InventoryReportItem.fromMap(row))
            .toList();

        yield ProgressiveLoadResult<InventoryReportItem>(
          loadId: loadId,
          data: items,
          currentPage: page + 1,
          totalPages: totalPages,
          totalItems: totalCount,
          isComplete: page == totalPages - 1,
          loadedItems: (page + 1) * pageSize,
        );

        if (page < totalPages - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي للمخزون',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'error': e.toString()},
      );

      yield ProgressiveLoadResult<InventoryReportItem>(
        loadId: loadId,
        data: [],
        currentPage: 0,
        totalPages: 0,
        totalItems: 0,
        isComplete: true,
        loadedItems: 0,
        error: e.toString(),
      );
    }
  }

  /// تحميل تدريجي للفواتير
  Stream<ProgressiveLoadResult<Map<String, dynamic>>> loadInvoicesProgressive({
    String? invoiceType,
    String? searchTerm,
    String? status,
    int pageSize = defaultPageSize,
  }) async* {
    final loadId = 'invoices_${DateTime.now().millisecondsSinceEpoch}';

    try {
      LoggingService.info(
        'بدء التحميل التدريجي للفواتير',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'pageSize': pageSize},
      );

      final db = await _databaseHelper.database;

      // بناء شروط البحث
      String whereClause = 'WHERE 1=1';
      List<dynamic> params = [];

      if (invoiceType != null && invoiceType.isNotEmpty) {
        whereClause += ' AND i.invoice_type = ?';
        params.add(invoiceType);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        whereClause +=
            ' AND (i.invoice_number LIKE ? OR c.name LIKE ? OR s.name LIKE ?)';
        params.addAll(['%$searchTerm%', '%$searchTerm%', '%$searchTerm%']);
      }

      if (status != null && status.isNotEmpty && status != 'all') {
        whereClause += ' AND i.status = ?';
        params.add(status);
      }

      // حساب العدد الإجمالي
      final countResult = await db.rawQuery('''
        SELECT COUNT(*) as total_count
        FROM ${AppConstants.invoicesTable} i
        LEFT JOIN ${AppConstants.customersTable} c ON i.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON i.supplier_id = s.id
        $whereClause
      ''', params);

      final totalCount = countResult.first['total_count'] as int;
      final totalPages = (totalCount / pageSize).ceil();

      for (int page = 0; page < totalPages; page++) {
        final offset = page * pageSize;

        final result = await db.rawQuery('''
          SELECT
            i.*,
            COALESCE(c.name, s.name) as party_name,
            CASE
              WHEN i.customer_id IS NOT NULL THEN 'customer'
              ELSE 'supplier'
            END as party_type
          FROM ${AppConstants.invoicesTable} i
          LEFT JOIN ${AppConstants.customersTable} c ON i.customer_id = c.id
          LEFT JOIN ${AppConstants.suppliersTable} s ON i.supplier_id = s.id
          $whereClause
          ORDER BY i.invoice_date DESC, i.id DESC
          LIMIT $pageSize OFFSET $offset
        ''', params);

        yield ProgressiveLoadResult<Map<String, dynamic>>(
          loadId: loadId,
          data: result,
          currentPage: page + 1,
          totalPages: totalPages,
          totalItems: totalCount,
          isComplete: page == totalPages - 1,
          loadedItems: (page + 1) * pageSize,
        );

        if (page < totalPages - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي للفواتير',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'error': e.toString()},
      );

      yield ProgressiveLoadResult<Map<String, dynamic>>(
        loadId: loadId,
        data: [],
        currentPage: 0,
        totalPages: 0,
        totalItems: 0,
        isComplete: true,
        loadedItems: 0,
        error: e.toString(),
      );
    }
  }

  /// تحميل تدريجي للحسابات
  Stream<ProgressiveLoadResult<Map<String, dynamic>>> loadAccountsProgressive({
    String? accountType,
    String? searchTerm,
    int pageSize = defaultPageSize,
  }) async* {
    final loadId = 'accounts_${DateTime.now().millisecondsSinceEpoch}';

    try {
      LoggingService.info(
        'بدء التحميل التدريجي للحسابات',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'pageSize': pageSize},
      );

      final db = await _databaseHelper.database;

      // بناء شروط البحث
      String whereClause = 'WHERE a.is_active = 1';
      List<dynamic> params = [];

      if (accountType != null &&
          accountType.isNotEmpty &&
          accountType != 'all') {
        whereClause += ' AND a.account_type = ?';
        params.add(accountType);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        whereClause += ' AND (a.name LIKE ? OR a.code LIKE ?)';
        params.addAll(['%$searchTerm%', '%$searchTerm%']);
      }

      // حساب العدد الإجمالي
      final countResult = await db.rawQuery('''
        SELECT COUNT(*) as total_count
        FROM ${AppConstants.accountsTable} a
        $whereClause
      ''', params);

      final totalCount = countResult.first['total_count'] as int;
      final totalPages = (totalCount / pageSize).ceil();

      for (int page = 0; page < totalPages; page++) {
        final offset = page * pageSize;

        final result = await db.rawQuery('''
          SELECT a.*
          FROM ${AppConstants.accountsTable} a
          $whereClause
          ORDER BY a.code ASC
          LIMIT $pageSize OFFSET $offset
        ''', params);

        yield ProgressiveLoadResult<Map<String, dynamic>>(
          loadId: loadId,
          data: result,
          currentPage: page + 1,
          totalPages: totalPages,
          totalItems: totalCount,
          isComplete: page == totalPages - 1,
          loadedItems: (page + 1) * pageSize,
        );

        if (page < totalPages - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي للحسابات',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'error': e.toString()},
      );

      yield ProgressiveLoadResult<Map<String, dynamic>>(
        loadId: loadId,
        data: [],
        currentPage: 0,
        totalPages: 0,
        totalItems: 0,
        isComplete: true,
        loadedItems: 0,
        error: e.toString(),
      );
    }
  }

  /// تحميل تدريجي للعملاء
  Stream<ProgressiveLoadResult<Map<String, dynamic>>> loadCustomersProgressive({
    String? searchTerm,
    bool? isActive,
    int pageSize = defaultPageSize,
  }) async* {
    final loadId = 'customers_${DateTime.now().millisecondsSinceEpoch}';

    try {
      LoggingService.info(
        'بدء التحميل التدريجي للعملاء',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'pageSize': pageSize},
      );

      final db = await _databaseHelper.database;

      // بناء شروط البحث
      String whereClause = 'WHERE 1=1';
      List<dynamic> params = [];

      if (isActive != null) {
        whereClause += ' AND c.is_active = ?';
        params.add(isActive ? 1 : 0);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        whereClause +=
            ' AND (c.name LIKE ? OR c.code LIKE ? OR c.phone LIKE ?)';
        params.addAll(['%$searchTerm%', '%$searchTerm%', '%$searchTerm%']);
      }

      // حساب العدد الإجمالي
      final countResult = await db.rawQuery('''
        SELECT COUNT(*) as total_count
        FROM ${AppConstants.customersTable} c
        $whereClause
      ''', params);

      final totalCount = countResult.first['total_count'] as int;
      final totalPages = (totalCount / pageSize).ceil();

      for (int page = 0; page < totalPages; page++) {
        final offset = page * pageSize;

        final result = await db.rawQuery('''
          SELECT c.*
          FROM ${AppConstants.customersTable} c
          $whereClause
          ORDER BY c.name ASC
          LIMIT $pageSize OFFSET $offset
        ''', params);

        yield ProgressiveLoadResult<Map<String, dynamic>>(
          loadId: loadId,
          data: result,
          currentPage: page + 1,
          totalPages: totalPages,
          totalItems: totalCount,
          isComplete: page == totalPages - 1,
          loadedItems: (page + 1) * pageSize,
        );

        if (page < totalPages - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي للعملاء',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'error': e.toString()},
      );

      yield ProgressiveLoadResult<Map<String, dynamic>>(
        loadId: loadId,
        data: [],
        currentPage: 0,
        totalPages: 0,
        totalItems: 0,
        isComplete: true,
        loadedItems: 0,
        error: e.toString(),
      );
    }
  }

  /// تحميل تدريجي للموردين
  Stream<ProgressiveLoadResult<Map<String, dynamic>>> loadSuppliersProgressive({
    String? searchTerm,
    bool? isActive,
    int pageSize = defaultPageSize,
  }) async* {
    final loadId = 'suppliers_${DateTime.now().millisecondsSinceEpoch}';

    try {
      LoggingService.info(
        'بدء التحميل التدريجي للموردين',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'pageSize': pageSize},
      );

      final db = await _databaseHelper.database;

      // بناء شروط البحث
      String whereClause = 'WHERE 1=1';
      List<dynamic> params = [];

      if (isActive != null) {
        whereClause += ' AND s.is_active = ?';
        params.add(isActive ? 1 : 0);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        whereClause +=
            ' AND (s.name LIKE ? OR s.code LIKE ? OR s.phone LIKE ?)';
        params.addAll(['%$searchTerm%', '%$searchTerm%', '%$searchTerm%']);
      }

      // حساب العدد الإجمالي
      final countResult = await db.rawQuery('''
        SELECT COUNT(*) as total_count
        FROM ${AppConstants.suppliersTable} s
        $whereClause
      ''', params);

      final totalCount = countResult.first['total_count'] as int;
      final totalPages = (totalCount / pageSize).ceil();

      for (int page = 0; page < totalPages; page++) {
        final offset = page * pageSize;

        final result = await db.rawQuery('''
          SELECT s.*
          FROM ${AppConstants.suppliersTable} s
          $whereClause
          ORDER BY s.name ASC
          LIMIT $pageSize OFFSET $offset
        ''', params);

        yield ProgressiveLoadResult<Map<String, dynamic>>(
          loadId: loadId,
          data: result,
          currentPage: page + 1,
          totalPages: totalPages,
          totalItems: totalCount,
          isComplete: page == totalPages - 1,
          loadedItems: (page + 1) * pageSize,
        );

        if (page < totalPages - 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في التحميل التدريجي للموردين',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId, 'error': e.toString()},
      );

      yield ProgressiveLoadResult<Map<String, dynamic>>(
        loadId: loadId,
        data: [],
        currentPage: 0,
        totalPages: 0,
        totalItems: 0,
        isComplete: true,
        loadedItems: 0,
        error: e.toString(),
      );
    }
  }

  /// إلغاء التحميل التدريجي
  void cancelProgressiveLoad(String loadId) {
    final controller = _activeLoads[loadId];
    if (controller != null) {
      controller.close();
      _activeLoads.remove(loadId);

      LoggingService.info(
        'تم إلغاء التحميل التدريجي',
        category: 'ProgressiveLoading',
        data: {'loadId': loadId},
      );
    }
  }

  /// إلغاء جميع التحميلات النشطة
  void cancelAllLoads() {
    for (final controller in _activeLoads.values) {
      controller.close();
    }
    _activeLoads.clear();

    LoggingService.info(
      'تم إلغاء جميع التحميلات التدريجية',
      category: 'ProgressiveLoading',
    );
  }

  /// الحصول على إحصائيات التحميل التدريجي
  ProgressiveLoadingStatistics getStatistics() {
    return ProgressiveLoadingStatistics(
      activeLoads: _activeLoads.length,
      totalLoadsStarted: _activeLoads.length, // سيتم تحسين هذا لاحقاً
    );
  }
}

/// نتيجة التحميل التدريجي
class ProgressiveLoadResult<T> {
  final String loadId;
  final List<T> data;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int loadedItems;
  final bool isComplete;
  final String? error;

  const ProgressiveLoadResult({
    required this.loadId,
    required this.data,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.loadedItems,
    required this.isComplete,
    this.error,
  });

  double get progressPercentage =>
      totalItems > 0 ? (loadedItems / totalItems) * 100 : 0;

  Map<String, dynamic> toMap() {
    return {
      'loadId': loadId,
      'dataCount': data.length,
      'currentPage': currentPage,
      'totalPages': totalPages,
      'totalItems': totalItems,
      'loadedItems': loadedItems,
      'isComplete': isComplete,
      'progressPercentage': progressPercentage,
      'error': error,
    };
  }
}

/// إحصائيات التحميل التدريجي
class ProgressiveLoadingStatistics {
  final int activeLoads;
  final int totalLoadsStarted;

  const ProgressiveLoadingStatistics({
    required this.activeLoads,
    required this.totalLoadsStarted,
  });

  Map<String, dynamic> toMap() {
    return {'activeLoads': activeLoads, 'totalLoadsStarted': totalLoadsStarted};
  }
}
