import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../models/report_models.dart';
import '../models/interactive_report_models.dart';
import 'optimized_reports_service.dart';
import 'modern_export_service.dart';
import 'warehouse_service.dart';
import 'employee_service.dart';
import 'logging_service.dart';
import 'database_migration_service.dart';
import 'reports_tables_checker.dart';
import 'safe_reports_service.dart';

class ReportsService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final OptimizedReportsService _optimizedService = OptimizedReportsService();
  final WarehouseService _warehouseService = WarehouseService();
  final EmployeeService _employeeService = EmployeeService();
  final DatabaseMigrationService _migrationService = DatabaseMigrationService();
  final ReportsTablesChecker _tablesChecker = ReportsTablesChecker();
  final SafeReportsService _safeReportsService = SafeReportsService();

  // تفعيل التحسينات افتراضياً
  bool useOptimizations = true;
  bool useCache = true;
  bool useProgressiveLoading = false;

  /// تهيئة الخدمة مع التحسينات
  Future<void> initialize() async {
    // التحقق من وجود الجداول المطلوبة
    await _ensureRequiredTablesExist();

    if (useOptimizations) {
      await _optimizedService.initialize();
    }
  }

  /// التحقق من وجود الجداول المطلوبة للتقارير
  Future<void> _ensureRequiredTablesExist() async {
    try {
      // فحص شامل لجميع الجداول والأعمدة المطلوبة
      final report = await _tablesChecker.getDetailedReport();

      if (report['database_ready_for_reports'] != true) {
        LoggingService.warning(
          'قاعدة البيانات تحتاج إصلاح للتقارير',
          category: 'ReportsService',
          data: report,
        );

        // محاولة إصلاح المشاكل
        final fixed = await _tablesChecker.fixAllIssues();

        if (fixed) {
          LoggingService.info(
            'تم إصلاح مشاكل قاعدة البيانات للتقارير',
            category: 'ReportsService',
          );
        } else {
          LoggingService.error(
            'فشل في إصلاح مشاكل قاعدة البيانات للتقارير',
            category: 'ReportsService',
          );
        }
      }

      // استخدام خدمة الترحيل للتحقق من الأعمدة المطلوبة وإضافة المفقود منها
      await _migrationService.ensureAllRequiredColumnsExist();

      // إصلاح مشاكل قاعدة البيانات الشائعة
      await _migrationService.fixCommonDatabaseIssues();

      // التحقق من جداول الموارد البشرية
      await _employeeService.ensureHRTablesExist();

      // التحقق من جداول المخزون
      await _warehouseService.ensureWarehouseTablesExist();
    } catch (e) {
      // تسجيل الخطأ ولكن لا نوقف التطبيق
      LoggingService.warning(
        'خطأ في التحقق من الجداول المطلوبة للتقارير',
        category: 'ReportsService',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على تقرير آمن للضرائب
  Future<List<Map<String, dynamic>>> getTaxReportSafe({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await _safeReportsService.getTaxReportSafe(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// الحصول على تقرير آمن للموردين
  Future<List<Map<String, dynamic>>> getSuppliersReportSafe({
    required DateTime startDate,
    required DateTime endDate,
    int? supplierId,
  }) async {
    return await _safeReportsService.getSuppliersReportSafe(
      startDate: startDate,
      endDate: endDate,
      supplierId: supplierId,
    );
  }

  /// تحديث إعدادات التحسين
  void updateOptimizationSettings({
    bool? enableOptimizations,
    bool? enableCache,
    bool? enableProgressiveLoading,
  }) {
    if (enableOptimizations != null) useOptimizations = enableOptimizations;
    if (enableCache != null) useCache = enableCache;
    if (enableProgressiveLoading != null) {
      useProgressiveLoading = enableProgressiveLoading;
    }
  }

  /// إلغاء التخزين المؤقت
  Future<void> clearCache({List<String>? tableNames}) async {
    if (useOptimizations && tableNames != null) {
      await _optimizedService.invalidateCache(tableNames: tableNames);
    }
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceStatistics() {
    if (useOptimizations) {
      return _optimizedService.getPerformanceStatistics().toMap();
    }
    return {};
  }

  // تقرير ميزان المراجعة محسن
  Future<List<TrialBalanceItem>> getTrialBalance({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    // استخدام الخدمة المحسنة إذا كانت مفعلة
    if (useOptimizations) {
      return await _optimizedService.getOptimizedTrialBalance(
        fromDate: fromDate,
        toDate: toDate,
        useCache: useCache,
        useProgressiveLoading: useProgressiveLoading,
      );
    }

    // الطريقة التقليدية
    final db = await _databaseHelper.database;

    // استعلام واحد محسن لحساب جميع الأرصدة
    final result = await db.rawQuery('''
      SELECT
        a.id,
        a.code,
        a.name,
        a.type,
        a.balance as opening_balance,
        COALESCE(SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0) as total_debit,
        COALESCE(SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0) as total_credit
      FROM ${AppConstants.accountsTable} a
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.is_active = 1 AND (je.is_posted = 1 OR je.id IS NULL)
        ${fromDate != null ? "AND je.entry_date >= '${fromDate.toIso8601String().split('T')[0]}'" : ''}
        ${toDate != null ? "AND je.entry_date <= '${toDate.toIso8601String().split('T')[0]}'" : ''}
      GROUP BY a.id, a.code, a.name, a.type, a.balance
      ORDER BY a.code ASC
    ''');

    return result.map((row) => TrialBalanceItem.fromMap(row)).toList();
  }

  // تقرير الأرباح والخسائر محسن
  Future<ProfitLossReportData> getProfitLoss(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // استخدام الخدمة المحسنة إذا كانت مفعلة
    if (useOptimizations) {
      return await _optimizedService.getOptimizedProfitLoss(
        fromDate: fromDate,
        toDate: toDate,
        useCache: useCache,
      );
    }

    // الطريقة التقليدية
    final db = await _databaseHelper.database;

    // الحصول على عناصر الإيرادات والمصروفات
    final itemsResult = await db.rawQuery(
      '''
      SELECT
        a.id as account_id,
        a.code as account_code,
        a.name as account_name,
        a.type,
        SUM(jed.debit_amount) as total_debit,
        SUM(jed.credit_amount) as total_credit
      FROM ${AppConstants.accountsTable} a
      JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
      JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE a.type IN ('${AppConstants.accountTypeRevenue}', '${AppConstants.accountTypeExpense}')
        AND je.is_posted = 1
        AND je.entry_date BETWEEN ? AND ?
      GROUP BY a.id, a.code, a.name, a.type
    ''',
      [
        fromDate.toIso8601String().split('T')[0],
        toDate.toIso8601String().split('T')[0],
      ],
    );

    List<ProfitLossItem> revenueItems = [];
    List<ProfitLossItem> expenseItems = [];
    double totalRevenue = 0;
    double totalExpense = 0;

    for (final row in itemsResult) {
      final type = row['type'] as String;
      final credit = (row['total_credit'] as num?)?.toDouble() ?? 0;
      final debit = (row['total_debit'] as num?)?.toDouble() ?? 0;
      final amount = type == AppConstants.accountTypeRevenue
          ? (credit - debit)
          : (debit - credit);

      final item = ProfitLossItem(
        accountId: row['account_id'] as int,
        accountCode: row['account_code'] as String,
        accountName: row['account_name'] as String,
        amount: amount,
      );

      if (type == AppConstants.accountTypeRevenue) {
        revenueItems.add(item);
        totalRevenue += amount;
      } else if (type == AppConstants.accountTypeExpense) {
        expenseItems.add(item);
        totalExpense += amount;
      }
    }

    return ProfitLossReportData(
      fromDate: fromDate,
      toDate: toDate,
      revenueItems: revenueItems,
      expenseItems: expenseItems,
      totalRevenue: totalRevenue,
      totalExpense: totalExpense,
      netProfit: totalRevenue - totalExpense,
    );
  }

  // تقرير الميزانية العمومية محسن
  Future<BalanceSheetReport> getBalanceSheet(DateTime asOfDate) async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery(
      '''
      SELECT 
        a.type,
        SUM(
          a.balance + 
          COALESCE(
            CASE 
              WHEN a.type IN ('${AppConstants.accountTypeAsset}', '${AppConstants.accountTypeExpense}') 
              THEN (
                SELECT SUM(jed.debit_amount - jed.credit_amount)
                FROM ${AppConstants.journalEntryDetailsTable} jed
                JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
                WHERE jed.account_id = a.id 
                  AND je.is_posted = 1 
                  AND je.entry_date <= ?
              )
              ELSE (
                SELECT SUM(jed.credit_amount - jed.debit_amount)
                FROM ${AppConstants.journalEntryDetailsTable} jed
                JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
                WHERE jed.account_id = a.id 
                  AND je.is_posted = 1 
                  AND je.entry_date <= ?
              )
            END, 0
          )
        ) as total_balance
      FROM ${AppConstants.accountsTable} a
      WHERE a.is_active = 1 
        AND a.type IN ('${AppConstants.accountTypeAsset}', '${AppConstants.accountTypeLiability}', '${AppConstants.accountTypeEquity}')
      GROUP BY a.type
    ''',
      [
        asOfDate.toIso8601String().split('T')[0],
        asOfDate.toIso8601String().split('T')[0],
      ],
    );

    double totalAssets = 0;
    double totalLiabilities = 0;
    double totalEquity = 0;

    for (final row in result) {
      final type = row['type'] as String;
      final balance = (row['total_balance'] as num).toDouble();

      switch (type) {
        case AppConstants.accountTypeAsset:
          totalAssets = balance;
          break;
        case AppConstants.accountTypeLiability:
          totalLiabilities = balance;
          break;
        case AppConstants.accountTypeEquity:
          totalEquity = balance;
          break;
      }
    }

    return BalanceSheetReport(
      asOfDate: asOfDate,
      totalAssets: totalAssets,
      totalLiabilities: totalLiabilities,
      totalEquity: totalEquity,
    );
  }

  // تقرير حركة الحسابات محسن
  Future<List<AccountMovementItem>> getAccountMovements(
    int accountId,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery(
      '''
      SELECT 
        je.entry_date,
        je.entry_number,
        je.description as entry_description,
        jed.debit_amount,
        jed.credit_amount,
        jed.description as detail_description
      FROM ${AppConstants.journalEntryDetailsTable} jed
      JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE jed.account_id = ? 
        AND je.is_posted = 1
        AND je.entry_date BETWEEN ? AND ?
      ORDER BY je.entry_date ASC, je.id ASC
    ''',
      [
        accountId,
        fromDate.toIso8601String().split('T')[0],
        toDate.toIso8601String().split('T')[0],
      ],
    );

    return result.map((row) => AccountMovementItem.fromMap(row)).toList();
  }

  // تقرير أعمار الديون محسن
  Future<List<AgingReportItem>> getCustomerAging() async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery('''
      SELECT 
        c.id,
        c.code,
        c.name,
        c.balance,
        CASE 
          WHEN julianday('now') - julianday(MAX(je.entry_date)) <= 30 THEN c.balance
          ELSE 0
        END as current_amount,
        CASE 
          WHEN julianday('now') - julianday(MAX(je.entry_date)) BETWEEN 31 AND 60 THEN c.balance
          ELSE 0
        END as days_31_60,
        CASE 
          WHEN julianday('now') - julianday(MAX(je.entry_date)) BETWEEN 61 AND 90 THEN c.balance
          ELSE 0
        END as days_61_90,
        CASE 
          WHEN julianday('now') - julianday(MAX(je.entry_date)) > 90 THEN c.balance
          ELSE 0
        END as over_90_days
      FROM ${AppConstants.customersTable} c
      LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON c.id = jed.account_id
      LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
      WHERE c.balance > 0 AND c.is_active = 1
      GROUP BY c.id, c.code, c.name, c.balance
      ORDER BY c.balance DESC
    ''');

    return result.map((row) => AgingReportItem.fromMap(row)).toList();
  }

  // تقرير أفضل العملاء محسن
  Future<List<TopCustomerItem>> getTopCustomers({int limit = 10}) async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery(
      '''
      SELECT 
        c.id,
        c.code,
        c.name,
        COUNT(DISTINCT i.id) as invoice_count,
        SUM(i.total_amount) as total_sales,
        AVG(i.total_amount) as average_invoice
      FROM ${AppConstants.customersTable} c
      JOIN ${AppConstants.invoicesTable} i ON c.id = i.customer_id
      WHERE c.is_active = 1 AND i.type = 'sale'
      GROUP BY c.id, c.code, c.name
      ORDER BY total_sales DESC
      LIMIT ?
    ''',
      [limit],
    );

    return result.map((row) => TopCustomerItem.fromMap(row)).toList();
  }

  // تقرير المخزون محسن
  Future<List<InventoryReportItem>> getInventoryReport() async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery('''
      SELECT 
        i.id,
        i.code,
        i.name,
        i.unit,
        i.quantity,
        i.min_quantity,
        i.cost_price,
        i.selling_price,
        (i.quantity * i.cost_price) as total_cost_value,
        (i.quantity * i.selling_price) as total_selling_value,
        CASE 
          WHEN i.quantity <= i.min_quantity THEN 'low_stock'
          WHEN i.quantity = 0 THEN 'out_of_stock'
          ELSE 'in_stock'
        END as stock_status
      FROM ${AppConstants.itemsTable} i
      WHERE i.is_active = 1
      ORDER BY 
        CASE 
          WHEN i.quantity = 0 THEN 1
          WHEN i.quantity <= i.min_quantity THEN 2
          ELSE 3
        END,
        i.name ASC
    ''');

    return result.map((row) => InventoryReportItem.fromMap(row)).toList();
  }

  /// تقرير المخزون المحسن
  Future<List<InventoryReportItem>> getOptimizedInventoryReport({
    String? category,
    String? searchTerm,
  }) async {
    // استخدام الخدمة المحسنة إذا كانت مفعلة
    if (useOptimizations) {
      return await _optimizedService.getOptimizedInventoryReport(
        category: category,
        searchTerm: searchTerm,
        useCache: useCache,
        useProgressiveLoading: useProgressiveLoading,
      );
    }

    // الطريقة التقليدية - استدعاء الدالة الموجودة
    return await getInventoryReport();
  }

  /// تحديث البيانات وإلغاء التخزين المؤقت
  Future<void> onDataUpdated(String tableName) async {
    if (useOptimizations) {
      await _optimizedService.invalidateCache(tableNames: [tableName]);
    }
  }

  /// تحديث البيانات لعدة جداول
  Future<void> onMultipleTablesUpdated(List<String> tableNames) async {
    if (useOptimizations) {
      await _optimizedService.invalidateCache(tableNames: tableNames);
    }
  }

  /// تصدير تقرير إلى PDF
  Future<String?> exportReportToPDF({
    required String reportTitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    Map<String, dynamic>? summary,
    Map<String, dynamic>? filters,
  }) async {
    return await ModernExportService.exportToPDF(
      reportTitle: reportTitle,
      data: data,
      headers: headers,
      summary: summary,
      filters: filters,
    );
  }

  /// تصدير تقرير إلى Excel
  Future<String?> exportReportToExcel({
    required String reportTitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    Map<String, dynamic>? summary,
    Map<String, dynamic>? filters,
  }) async {
    return await ModernExportService.exportToExcel(
      reportTitle: reportTitle,
      data: data,
      headers: headers,
      summary: summary,
      filters: filters,
    );
  }

  /// تصدير تقرير إلى CSV
  Future<String?> exportReportToCSV({
    required String reportTitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    Map<String, dynamic>? summary,
  }) async {
    return await ModernExportService.exportToCSV(
      reportTitle: reportTitle,
      data: data,
      headers: headers,
      summary: summary,
    );
  }

  /// مشاركة ملف تقرير
  Future<void> shareReportFile(String filePath, String title) async {
    await ModernExportService.shareFile(filePath, title);
  }

  /// تصدير ميزان المراجعة
  Future<String?> exportTrialBalanceToPDF({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final data = await getTrialBalance(fromDate: fromDate, toDate: toDate);
    final dataMap = data.map((item) => item.toMap()).toList();

    final headers = [
      'الكود',
      'اسم الحساب',
      'النوع',
      'المدين',
      'الدائن',
      'الرصيد',
    ];

    final filters = <String, dynamic>{};
    if (fromDate != null) {
      filters['من تاريخ'] = fromDate.toString().split(' ')[0];
    }
    if (toDate != null) {
      filters['إلى تاريخ'] = toDate.toString().split(' ')[0];
    }

    final totalDebit = data.fold(0.0, (sum, item) => sum + item.totalDebit);
    final totalCredit = data.fold(0.0, (sum, item) => sum + item.totalCredit);

    final summary = {
      'إجمالي المدين': totalDebit.toStringAsFixed(2),
      'إجمالي الدائن': totalCredit.toStringAsFixed(2),
      'عدد الحسابات': data.length.toString(),
    };

    return await exportReportToPDF(
      reportTitle: 'ميزان المراجعة',
      data: dataMap,
      headers: headers,
      summary: summary,
      filters: filters,
    );
  }

  /// تصدير قائمة الدخل
  Future<String?> exportProfitLossToPDF({
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    final report = await getProfitLoss(fromDate, toDate);

    final data = [
      {
        'البيان': 'إجمالي الإيرادات',
        'المبلغ': report.totalRevenue.toStringAsFixed(2),
      },
      {
        'البيان': 'إجمالي المصروفات',
        'المبلغ': report.totalExpense.toStringAsFixed(2),
      },
      {
        'البيان': 'صافي الربح/الخسارة',
        'المبلغ': report.netProfit.toStringAsFixed(2),
      },
    ];

    final headers = ['البيان', 'المبلغ'];

    final filters = {
      'من تاريخ': fromDate.toString().split(' ')[0],
      'إلى تاريخ': toDate.toString().split(' ')[0],
    };

    final summary = {
      'نوع النتيجة': report.netProfit >= 0 ? 'ربح' : 'خسارة',
      'نسبة الربح': report.totalRevenue > 0
          ? '${((report.netProfit / report.totalRevenue) * 100).toStringAsFixed(2)}%'
          : '0%',
    };

    return await exportReportToPDF(
      reportTitle: 'قائمة الدخل',
      data: data,
      headers: headers,
      summary: summary,
      filters: filters,
    );
  }
}

class BalanceSheetReport {
  final DateTime asOfDate;
  final double totalAssets;
  final double totalLiabilities;
  final double totalEquity;

  BalanceSheetReport({
    required this.asOfDate,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.totalEquity,
  });
}

class TopCustomerItem {
  final int id;
  final String code;
  final String name;
  final int invoiceCount;
  final double totalSales;
  final double averageInvoice;

  TopCustomerItem({
    required this.id,
    required this.code,
    required this.name,
    required this.invoiceCount,
    required this.totalSales,
    required this.averageInvoice,
  });

  factory TopCustomerItem.fromMap(Map<String, dynamic> map) {
    return TopCustomerItem(
      id: map['id'] as int,
      code: map['code'] as String,
      name: map['name'] as String,
      invoiceCount: map['invoice_count'] as int,
      totalSales: (map['total_sales'] as num).toDouble(),
      averageInvoice: (map['average_invoice'] as num).toDouble(),
    );
  }
}
