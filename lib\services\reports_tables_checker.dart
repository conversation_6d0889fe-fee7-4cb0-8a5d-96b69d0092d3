/// خدمة التحقق من جداول التقارير
/// توفر طرق للتحقق من وجود جميع الجداول والأعمدة المطلوبة للتقارير
library;

import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/database_migration_service.dart';

class ReportsTablesChecker {
  static final ReportsTablesChecker _instance =
      ReportsTablesChecker._internal();
  factory ReportsTablesChecker() => _instance;
  ReportsTablesChecker._internal();

  final DatabaseMigrationService _migrationService = DatabaseMigrationService();

  /// التحقق من جميع الجداول المطلوبة للتقارير
  Future<Map<String, bool>> checkAllRequiredTables() async {
    final results = <String, bool>{};

    try {
      // قائمة الجداول المطلوبة
      final requiredTables = [
        AppConstants.accountsTable,
        AppConstants.journalEntriesTable,
        AppConstants.journalEntryDetailsTable,
        AppConstants.invoicesTable,
        AppConstants.invoiceItemsTable,
        AppConstants.customersTable,
        AppConstants.suppliersTable,
        AppConstants.paymentsTable,
        AppConstants.itemsTable,
        AppConstants.warehousesTable,
        AppConstants.itemLocationsTable,
        AppConstants.employeesTable,
      ];

      for (final tableName in requiredTables) {
        final exists = await _migrationService.tableExists(tableName);
        results[tableName] = exists;

        if (!exists) {
          LoggingService.warning(
            'الجدول المطلوب غير موجود: $tableName',
            category: 'ReportsTablesChecker',
          );
        }
      }

      LoggingService.info(
        'تم فحص جميع الجداول المطلوبة للتقارير',
        category: 'ReportsTablesChecker',
        data: {
          'total_tables': requiredTables.length,
          'missing_tables': results.values.where((v) => !v).length,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص الجداول المطلوبة',
        category: 'ReportsTablesChecker',
        data: {'error': e.toString()},
      );
    }

    return results;
  }

  /// التحقق من الأعمدة المطلوبة في جداول التقارير
  Future<Map<String, Map<String, bool>>> checkRequiredColumns() async {
    final results = <String, Map<String, bool>>{};

    try {
      // الأعمدة المطلوبة لكل جدول
      final requiredColumns = {
        AppConstants.accountsTable: [
          'opening_balance',
          'account_type',
          'balance',
        ],
        AppConstants.journalEntriesTable: [
          'reference',
          'reference_type',
          'reference_id',
        ],
        AppConstants.invoicesTable: [
          'invoice_type',
          'due_date',
          'terms',
          'reference',
        ],
        AppConstants.paymentsTable: [
          'amount',
          'method',
          'status',
          'payment_date',
        ],
        AppConstants.itemLocationsTable: [
          'quantity',
          'warehouse_id',
          'item_id',
        ],
      };

      for (final entry in requiredColumns.entries) {
        final tableName = entry.key;
        final columns = entry.value;

        results[tableName] = {};

        for (final columnName in columns) {
          final exists = await _migrationService.columnExists(
            tableName,
            columnName,
          );
          results[tableName]![columnName] = exists;

          if (!exists) {
            LoggingService.warning(
              'العمود المطلوب غير موجود: $columnName في الجدول: $tableName',
              category: 'ReportsTablesChecker',
            );
          }
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص الأعمدة المطلوبة',
        category: 'ReportsTablesChecker',
        data: {'error': e.toString()},
      );
    }

    return results;
  }

  /// إصلاح جميع المشاكل المكتشفة
  Future<bool> fixAllIssues() async {
    try {
      LoggingService.info(
        'بدء إصلاح مشاكل جداول التقارير',
        category: 'ReportsTablesChecker',
      );

      // إصلاح مشاكل قاعدة البيانات الشائعة
      await _migrationService.fixCommonDatabaseIssues();

      // التحقق من النتائج بعد الإصلاح
      final tablesCheck = await checkAllRequiredTables();
      final columnsCheck = await checkRequiredColumns();

      final missingTables = tablesCheck.values.where((v) => !v).length;
      final missingColumns = columnsCheck.values
          .expand((tableColumns) => tableColumns.values)
          .where((v) => !v)
          .length;

      if (missingTables == 0 && missingColumns == 0) {
        LoggingService.info(
          'تم إصلاح جميع مشاكل جداول التقارير بنجاح',
          category: 'ReportsTablesChecker',
        );
        return true;
      } else {
        LoggingService.warning(
          'لا تزال هناك مشاكل في جداول التقارير',
          category: 'ReportsTablesChecker',
          data: {
            'missing_tables': missingTables,
            'missing_columns': missingColumns,
          },
        );
        return false;
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إصلاح مشاكل جداول التقارير',
        category: 'ReportsTablesChecker',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على تقرير مفصل عن حالة الجداول
  Future<Map<String, dynamic>> getDetailedReport() async {
    try {
      final tablesCheck = await checkAllRequiredTables();
      final columnsCheck = await checkRequiredColumns();

      final missingTables = tablesCheck.entries
          .where((entry) => !entry.value)
          .map((entry) => entry.key)
          .toList();

      final missingColumns = <String, List<String>>{};
      for (final entry in columnsCheck.entries) {
        final tableName = entry.key;
        final tableColumns = entry.value;

        final missing = tableColumns.entries
            .where((colEntry) => !colEntry.value)
            .map((colEntry) => colEntry.key)
            .toList();

        if (missing.isNotEmpty) {
          missingColumns[tableName] = missing;
        }
      }

      return {
        'total_tables_checked': tablesCheck.length,
        'missing_tables_count': missingTables.length,
        'missing_tables': missingTables,
        'total_columns_checked': columnsCheck.values
            .expand((tableColumns) => tableColumns.values)
            .length,
        'missing_columns_count': columnsCheck.values
            .expand((tableColumns) => tableColumns.values)
            .where((v) => !v)
            .length,
        'missing_columns': missingColumns,
        'all_tables_exist': missingTables.isEmpty,
        'all_columns_exist': missingColumns.isEmpty,
        'database_ready_for_reports':
            missingTables.isEmpty && missingColumns.isEmpty,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء التقرير المفصل',
        category: 'ReportsTablesChecker',
        data: {'error': e.toString()},
      );

      return {
        'error': true,
        'error_message': e.toString(),
        'database_ready_for_reports': false,
      };
    }
  }

  /// طباعة تقرير مفصل في وحدة التحكم
  Future<void> printDetailedReport() async {
    final report = await getDetailedReport();

    if (kDebugMode) {
      print('\n=== تقرير حالة جداول التقارير ===');
    }
    if (kDebugMode) {
      print('إجمالي الجداول المفحوصة: ${report['total_tables_checked']}');
    }
    if (kDebugMode) {
      print('الجداول المفقودة: ${report['missing_tables_count']}');
    }

    if (report['missing_tables'] != null &&
        (report['missing_tables'] as List).isNotEmpty) {
      if (kDebugMode) {
        print('قائمة الجداول المفقودة:');
      }
      for (final table in report['missing_tables'] as List) {
        if (kDebugMode) {
          if (kDebugMode) {}
          print('  - $table');
        }
      }
    }

    if (kDebugMode) {
      print('\nإجمالي الأعمدة المفحوصة: ${report['total_columns_checked']}');
    }
    if (kDebugMode) {
      print('الأعمدة المفقودة: ${report['missing_columns_count']}');
    }

    if (report['missing_columns'] != null &&
        (report['missing_columns'] as Map).isNotEmpty) {
      if (kDebugMode) {
        print('قائمة الأعمدة المفقودة:');
      }
      for (final entry
          in (report['missing_columns'] as Map<String, List<String>>).entries) {
        if (kDebugMode) {
          print('  الجدول: ${entry.key}');
        }
        for (final column in entry.value) {
          if (kDebugMode) {
            print('    - $column');
          }
        }
      }
    }

    if (kDebugMode) {
      print('=====================================\n');
    }
  }
}
