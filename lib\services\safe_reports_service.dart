/// خدمة التقارير الآمنة
/// توفر طرق آمنة لإنشاء التقارير مع معالجة الأعمدة المفقودة
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/database_migration_service.dart';

class SafeReportsService {
  static final SafeReportsService _instance = SafeReportsService._internal();
  factory SafeReportsService() => _instance;
  SafeReportsService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final DatabaseMigrationService _migrationService = DatabaseMigrationService();

  /// تقرير الضرائب الآمن
  Future<List<Map<String, dynamic>>> getTaxReportSafe({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // التحقق من وجود العمود المطلوب
      await _migrationService.ensureAllRequiredColumnsExist();

      final db = await _databaseHelper.database;

      // استعلام آمن مع التحقق من وجود الأعمدة
      final query =
          '''
        SELECT 
          i.id,
          i.invoice_number,
          i.invoice_date,
          COALESCE(i.invoice_type, i.type, 'sale') as invoice_type,
          i.subtotal,
          i.tax_amount,
          i.total_amount,
          CASE 
            WHEN i.customer_id IS NOT NULL THEN c.name
            WHEN i.supplier_id IS NOT NULL THEN s.name
            ELSE 'غير محدد'
          END as party_name
        FROM ${AppConstants.invoicesTable} i
        LEFT JOIN ${AppConstants.customersTable} c ON i.customer_id = c.id
        LEFT JOIN ${AppConstants.suppliersTable} s ON i.supplier_id = s.id
        WHERE i.invoice_date BETWEEN ? AND ?
          AND i.tax_amount > 0
        ORDER BY i.invoice_date DESC
      ''';

      final result = await db.rawQuery(query, [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ]);

      LoggingService.info(
        'تم إنشاء تقرير الضرائب بنجاح',
        category: 'SafeReportsService',
        data: {'records_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير الضرائب',
        category: 'SafeReportsService',
        data: {'error': e.toString()},
      );

      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }

  /// تقرير اليومية الآمن
  Future<List<Map<String, dynamic>>> getJournalReportSafe({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // التحقق من وجود العمود المطلوب
      await _migrationService.ensureAllRequiredColumnsExist();

      final db = await _databaseHelper.database;

      // استعلام آمن مع التحقق من وجود الأعمدة
      final query =
          '''
        SELECT 
          je.id,
          je.entry_number,
          je.entry_date,
          je.description,
          je.type,
          COALESCE(je.reference, je.reference_type || '-' || COALESCE(je.reference_id, ''), 'بدون مرجع') as reference,
          je.total_debit,
          je.total_credit,
          je.is_posted
        FROM ${AppConstants.journalEntriesTable} je
        WHERE je.entry_date BETWEEN ? AND ?
        ORDER BY je.entry_date DESC, je.entry_number DESC
      ''';

      final result = await db.rawQuery(query, [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ]);

      LoggingService.info(
        'تم إنشاء تقرير اليومية بنجاح',
        category: 'SafeReportsService',
        data: {'records_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير اليومية',
        category: 'SafeReportsService',
        data: {'error': e.toString()},
      );

      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }

  /// تقرير الأستاذ العام الآمن
  Future<List<Map<String, dynamic>>> getGeneralLedgerReportSafe({
    required DateTime startDate,
    required DateTime endDate,
    int? accountId,
  }) async {
    try {
      // التحقق من وجود العمود المطلوب
      await _migrationService.ensureAllRequiredColumnsExist();

      final db = await _databaseHelper.database;

      // استعلام آمن مع التحقق من وجود الأعمدة
      String whereClause = 'WHERE je.entry_date BETWEEN ? AND ?';
      List<dynamic> params = [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ];

      if (accountId != null) {
        whereClause += ' AND jed.account_id = ?';
        params.add(accountId);
      }

      final query =
          '''
        SELECT 
          a.id,
          a.code,
          a.name,
          a.type,
          COALESCE(a.opening_balance, a.balance, 0.0) as opening_balance,
          SUM(COALESCE(jed.debit_amount, 0.0)) as total_debit,
          SUM(COALESCE(jed.credit_amount, 0.0)) as total_credit,
          (COALESCE(a.opening_balance, a.balance, 0.0) + 
           SUM(COALESCE(jed.debit_amount, 0.0)) - 
           SUM(COALESCE(jed.credit_amount, 0.0))) as closing_balance
        FROM ${AppConstants.accountsTable} a
        LEFT JOIN ${AppConstants.journalEntryDetailsTable} jed ON a.id = jed.account_id
        LEFT JOIN ${AppConstants.journalEntriesTable} je ON jed.journal_entry_id = je.id
        $whereClause
        GROUP BY a.id, a.code, a.name, a.type, a.opening_balance, a.balance
        HAVING (SUM(COALESCE(jed.debit_amount, 0.0)) > 0 OR 
                SUM(COALESCE(jed.credit_amount, 0.0)) > 0 OR 
                COALESCE(a.opening_balance, a.balance, 0.0) != 0)
        ORDER BY a.code
      ''';

      final result = await db.rawQuery(query, params);

      LoggingService.info(
        'تم إنشاء تقرير الأستاذ العام بنجاح',
        category: 'SafeReportsService',
        data: {'records_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير الأستاذ العام',
        category: 'SafeReportsService',
        data: {'error': e.toString()},
      );

      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }

  /// تقرير المخزون الآمن
  Future<List<Map<String, dynamic>>> getInventoryReportSafe({
    int? warehouseId,
    bool lowStockOnly = false,
  }) async {
    try {
      // التحقق من وجود الجداول والأعمدة المطلوبة
      await _migrationService.ensureAllRequiredColumnsExist();

      final db = await _databaseHelper.database;

      // التحقق من وجود جدول item_locations
      final tableExists = await _migrationService.tableExists(
        AppConstants.itemLocationsTable,
      );

      if (!tableExists) {
        LoggingService.warning(
          'جدول مواقع الأصناف غير موجود، سيتم إرجاع قائمة فارغة',
          category: 'SafeReportsService',
        );
        return [];
      }

      String whereClause = 'WHERE i.is_active = 1';
      List<dynamic> params = [];

      if (warehouseId != null) {
        whereClause += ' AND il.warehouse_id = ?';
        params.add(warehouseId);
      }

      if (lowStockOnly) {
        whereClause += ' AND il.quantity <= COALESCE(il.reorder_point, 0)';
      }

      final query =
          '''
        SELECT 
          i.id,
          i.code,
          i.name,
          i.unit,
          w.name as warehouse_name,
          COALESCE(il.quantity, 0.0) as current_quantity,
          COALESCE(il.reserved_quantity, 0.0) as reserved_quantity,
          (COALESCE(il.quantity, 0.0) - COALESCE(il.reserved_quantity, 0.0)) as available_quantity,
          COALESCE(il.min_stock_level, i.min_quantity, 0.0) as min_stock_level,
          COALESCE(il.reorder_point, 0.0) as reorder_point,
          i.cost_price,
          i.selling_price,
          (COALESCE(il.quantity, 0.0) * COALESCE(i.cost_price, 0.0)) as total_value
        FROM ${AppConstants.itemsTable} i
        LEFT JOIN ${AppConstants.itemLocationsTable} il ON i.id = il.item_id
        LEFT JOIN ${AppConstants.warehousesTable} w ON il.warehouse_id = w.id
        $whereClause
        ORDER BY i.code, w.name
      ''';

      final result = await db.rawQuery(query, params);

      LoggingService.info(
        'تم إنشاء تقرير المخزون بنجاح',
        category: 'SafeReportsService',
        data: {'records_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير المخزون',
        category: 'SafeReportsService',
        data: {'error': e.toString()},
      );

      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }

  /// تقرير المبيعات الآمن
  Future<List<Map<String, dynamic>>> getSalesReportSafe({
    required DateTime startDate,
    required DateTime endDate,
    int? customerId,
  }) async {
    try {
      // التحقق من وجود العمود المطلوب
      await _migrationService.ensureAllRequiredColumnsExist();

      final db = await _databaseHelper.database;

      String whereClause = '''
        WHERE i.invoice_date BETWEEN ? AND ?
          AND (COALESCE(i.invoice_type, i.type, 'sale') = 'sale' OR i.type = 'sale')
      ''';
      List<dynamic> params = [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ];

      if (customerId != null) {
        whereClause += ' AND i.customer_id = ?';
        params.add(customerId);
      }

      final query =
          '''
        SELECT 
          i.id,
          i.invoice_number,
          i.invoice_date,
          c.name as customer_name,
          i.subtotal,
          i.tax_amount,
          i.discount_amount,
          i.total_amount,
          i.paid_amount,
          i.remaining_amount,
          i.status
        FROM ${AppConstants.invoicesTable} i
        LEFT JOIN ${AppConstants.customersTable} c ON i.customer_id = c.id
        $whereClause
        ORDER BY i.invoice_date DESC, i.invoice_number DESC
      ''';

      final result = await db.rawQuery(query, params);

      LoggingService.info(
        'تم إنشاء تقرير المبيعات بنجاح',
        category: 'SafeReportsService',
        data: {'records_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير المبيعات',
        category: 'SafeReportsService',
        data: {'error': e.toString()},
      );

      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }

  /// تقرير الموردين الآمن
  Future<List<Map<String, dynamic>>> getSuppliersReportSafe({
    required DateTime startDate,
    required DateTime endDate,
    int? supplierId,
  }) async {
    try {
      // التحقق من وجود الجداول والأعمدة المطلوبة
      await _migrationService.ensureAllRequiredColumnsExist();

      final db = await _databaseHelper.database;

      // التحقق من وجود جدول المدفوعات
      final paymentsTableExists = await _migrationService.tableExists(
        AppConstants.paymentsTable,
      );

      String paymentsJoin = '';
      String paymentsSelect = '0.0 as total_payments';

      if (paymentsTableExists) {
        paymentsJoin =
            '''
          LEFT JOIN ${AppConstants.paymentsTable} p ON i.id = p.invoice_id
            AND p.status = 'completed'
        ''';
        paymentsSelect = 'COALESCE(SUM(p.amount), 0.0) as total_payments';
      }

      String whereClause = '''
        WHERE i.invoice_date BETWEEN ? AND ?
          AND (COALESCE(i.invoice_type, i.type, 'purchase') = 'purchase' OR i.type = 'purchase')
          AND i.supplier_id IS NOT NULL
      ''';
      List<dynamic> params = [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ];

      if (supplierId != null) {
        whereClause += ' AND i.supplier_id = ?';
        params.add(supplierId);
      }

      final query =
          '''
        SELECT
          s.id,
          s.name as supplier_name,
          s.phone,
          s.email,
          s.address,
          COUNT(i.id) as total_invoices,
          COALESCE(SUM(i.total_amount), 0.0) as total_amount,
          $paymentsSelect,
          (COALESCE(SUM(i.total_amount), 0.0) - COALESCE(SUM(p.amount), 0.0)) as remaining_amount
        FROM ${AppConstants.suppliersTable} s
        LEFT JOIN ${AppConstants.invoicesTable} i ON s.id = i.supplier_id
        $paymentsJoin
        $whereClause
        GROUP BY s.id, s.name, s.phone, s.email, s.address
        HAVING COUNT(i.id) > 0 OR COALESCE(SUM(i.total_amount), 0.0) > 0
        ORDER BY s.name
      ''';

      final result = await db.rawQuery(query, params);

      LoggingService.info(
        'تم إنشاء تقرير الموردين بنجاح',
        category: 'SafeReportsService',
        data: {'records_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير الموردين',
        category: 'SafeReportsService',
        data: {'error': e.toString()},
      );

      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }

  /// تقرير التصدير المتقدم الآمن للحسابات
  Future<List<Map<String, dynamic>>> getAdvancedAccountsExportSafe({
    String? accountType,
    bool activeOnly = true,
  }) async {
    try {
      // التحقق من وجود الأعمدة المطلوبة
      await _migrationService.ensureAllRequiredColumnsExist();

      final db = await _databaseHelper.database;

      String whereClause = 'WHERE 1=1';
      List<dynamic> params = [];

      if (activeOnly) {
        whereClause += ' AND a.is_active = 1';
      }

      if (accountType != null && accountType.isNotEmpty) {
        // استخدام COALESCE للتعامل مع العمود المفقود
        whereClause +=
            ' AND (COALESCE(a.account_type, a.type) = ? OR a.type = ?)';
        params.addAll([accountType, accountType]);
      }

      final query =
          '''
        SELECT
          a.id,
          a.code,
          a.name,
          a.type,
          COALESCE(a.account_type, a.type, 'غير محدد') as account_type,
          a.level,
          a.is_active,
          COALESCE(a.balance, 0.0) as current_balance,
          COALESCE(a.opening_balance, 0.0) as opening_balance,
          a.description,
          a.created_at,
          a.updated_at,
          CASE
            WHEN a.parent_id IS NOT NULL THEN pa.name
            ELSE 'حساب رئيسي'
          END as parent_account_name
        FROM ${AppConstants.accountsTable} a
        LEFT JOIN ${AppConstants.accountsTable} pa ON a.parent_id = pa.id
        $whereClause
        ORDER BY a.code, a.name
      ''';

      final result = await db.rawQuery(query, params);

      LoggingService.info(
        'تم إنشاء تقرير التصدير المتقدم للحسابات بنجاح',
        category: 'SafeReportsService',
        data: {'records_count': result.length},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير التصدير المتقدم للحسابات',
        category: 'SafeReportsService',
        data: {'error': e.toString()},
      );

      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }
}
