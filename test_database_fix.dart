import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'lib/services/database_migration_service.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  if (kDebugMode) {
    print('بدء اختبار إصلاح قاعدة البيانات...');
  }
  
  try {
    final migrationService = DatabaseMigrationService();
    
    // اختبار إصلاح مشاكل قاعدة البيانات
    await migrationService.fixCommonDatabaseIssues();
    
    if (kDebugMode) {
      print('✅ تم إصلاح قاعدة البيانات بنجاح');
    }
    
    // اختبار فحص وجود العمود
    final hasColumn = await migrationService.columnExists('item_locations', 'available_quantity');
    if (kDebugMode) {
      print('العمود available_quantity موجود: $hasColumn');
    }
    
  } catch (e) {
    if (kDebugMode) {
      print('❌ خطأ في إصلاح قاعدة البيانات: $e');
    }
  }
}
