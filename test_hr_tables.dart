import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'lib/services/database_migration_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('بدء اختبار جداول الموارد البشرية...');
  }

  try {
    final migrationService = DatabaseMigrationService();

    // اختبار فحص وجود الجداول
    final attendanceExists = await migrationService.tableExists('attendance');
    final leavesExists = await migrationService.tableExists('leaves');
    final loansExists = await migrationService.tableExists('loans');
    final departmentsExists = await migrationService.tableExists('departments');
    final positionsExists = await migrationService.tableExists('positions');

    if (kDebugMode) {
      print('جدول الحضور موجود: $attendanceExists');
      print('جدول الإجازات موجود: $leavesExists');
      print('جدول القروض موجود: $loansExists');
      print('جدول الأقسام موجود: $departmentsExists');
      print('جدول المناصب موجود: $positionsExists');
    }

    if (!attendanceExists || !leavesExists || !loansExists || !departmentsExists || !positionsExists) {
      if (kDebugMode) {
        print('بعض الجداول مفقودة، سيتم إنشاؤها...');
      }
      
      // تشغيل إصلاح قاعدة البيانات
      await migrationService.fixCommonDatabaseIssues();
      
      if (kDebugMode) {
        print('✅ تم إصلاح جداول الموارد البشرية بنجاح');
      }
    } else {
      if (kDebugMode) {
        print('✅ جميع جداول الموارد البشرية موجودة');
      }
    }

  } catch (e) {
    if (kDebugMode) {
      print('❌ خطأ في اختبار جداول الموارد البشرية: $e');
    }
  }
}
